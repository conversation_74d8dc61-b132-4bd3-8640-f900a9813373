#ifndef VCRDEV_H
#define VCRDEV_H
#include "CusH264Struct.h"
#include "abstractdev.h"
#include "globalutils.h"
#include "lanetype.h"
#include "pausablethread.h"
#include "vcrtypes.h"
#include "httplistener.h"
#include "vcrrequesthandler.h"

using namespace stefanfrings;

// 释放内存宏
#define SafeDelMemory(pNew, isArray) \
    {                                \
        if (pNew != NULL) {          \
            if (isArray)             \
                delete[] pNew;       \
            else                     \
                delete pNew;         \
        }                            \
        pNew = NULL;                 \
    }

// 图像定义

/* 设备结果通知使用，开发中发现，直接在设备回调函数中
发送事件，关连的槽函数无法触发，无找到原因，所以在监护
线程中发送事件*/
typedef struct tagVcrNotify
{
    quint32 dwCarId;
    QString sPlate;  //车牌号码，不包含颜色
    int nColor;      //车牌颜色
    CVehClass vehClass;
    int nAxisCount;
    tagVcrNotify(DWORD carId, int color, const QString &plate, CVehClass vc, int axiscount)
    {
        dwCarId = carId;
        nColor = color;
        sPlate = plate;
        vehClass = vc;
        nAxisCount = axiscount;
    }
    tagVcrNotify()
    {
        dwCarId = 0;
        nColor = 0;
        sPlate.clear();
        vehClass = VC_None;
        nAxisCount = 0;
    }
} VcrNotify;

class VCRDev;

// 设备状态监护线程，检测设备状态变化
class CVcrThread : public CPausableThread
{
    Q_OBJECT
public:
    CVcrThread() : CPausableThread()
    {
        setObjectName(QString("VcrsDetectThread"));
        m_pVcrDev = NULL;
        m_waitTime = 10;
    }
    void SetVcrDev(VCRDev *pDev) { m_pVcrDev = pDev; }

protected:
    virtual bool RunOnce(void);

protected:
    VCRDev *m_pVcrDev;
};

/**
 * @brief The VCRDev class 车型识别设备
 */
class VCRDev : public CAbstractDev
{
    Q_OBJECT
public:
    VCRDev();
    virtual ~VCRDev();

protected:
    // 为防止重复加载动态库，此处使用静态变量
    static bool m_bDriverLoaded;
    static QLibrary m_hLibModule;
    //打开的设备句柄
    PVOID m_hDevice;
    // 当前设备返回来的结果
    VcrResult *m_pcurHvResult;
    // 结果链表
    QList<VcrResult *> m_listHvResult;
    //保护结果链表对象
    QMutex m_LockResult;

    QMutex m_LockPreResult;
    QList<VcrResult *> m_preResult;

    //图像目录
    QString m_sImagePath;

    //上次检测连接状态时间
    QDateTime m_dtLastCheckConnect;
    //上次连接状态
    bool m_bConnectStatus;

    //工作线程对象
    CVcrThread m_WorkThread;
    QMutex m_FrameMt;
    QList<CustH264Struct *> m_FrameList;

    QMutex m_cardIdMutex;
    QList<VideoCarIdInfo> m_CarIdList;

    bool m_bSendVideo;  //是否传输小视频

    //

    stefanfrings::HttpListener *m_pHttpListener;
    VcrRequestHandler *m_pRequestHandler;

protected:
    /****************回调函数**************/
    // 识别结果开始回调函数
    static int _cdecl OnHvResultBeginProxy(PVOID pUserData, DWORD dwCarID);
    int OnHvResultBegin(DWORD dwCarID);
    // 识别结果结束回调函数
    static int _cdecl OnHvResultEndProxy(PVOID pUserData, DWORD dwCarID);
    int OnHvResultEnd(DWORD dwCarID);
    // 车牌信息回调
    static int _cdecl OnHvResultPlateProxy(PVOID pUserData, DWORD dwCarID, LPCSTR pcPlateNo,
                                           LPCSTR pcAppendInfo, DWORD dwRecordType,
                                           DWORD64 dw64TimeMS);
    int OnHvResultPlate(DWORD dwCarID, LPCSTR pcPlateNo, LPCSTR pcAppendInfo, DWORD dwRecordType,
                        DWORD64 dw64TimeMS);

    // 大图回调
    static int _cdecl OnHvResultBigImageProxy(PVOID pUserData, DWORD dwCarID, WORD wImgType,
                                              WORD wWidth, WORD wHeight, PBYTE pbPicData,
                                              DWORD dwImgDataLen, DWORD dwRecordType,
                                              DWORD64 dw64TimeMS);
    int OnHvResultBigImage(DWORD dwCarID, WORD wImgType, WORD wWidth, WORD wHeight, PBYTE pbPicData,
                           DWORD dwImgDataLen, DWORD dwRecordType, DWORD64 dw64TimeMS);

    // 车牌小图回调
    static int _cdecl OnHvResultSmallImageProxy(PVOID pUserData, DWORD dwCarID, WORD wWidth,
                                                WORD wHeight, PBYTE pbPicData, DWORD dwImgDataLen,
                                                DWORD dwRecordType, DWORD64 dw64TimeMS);
    int OnHvResultSmallImage(DWORD dwCarID, WORD wWidth, WORD wHeight, PBYTE pbPicData,
                             DWORD dwImgDataLen, DWORD dwRecordType, DWORD64 dw64TimeMS);

    // 车牌二值图回调
    static int _cdecl OnHvResultBinImageProxy(PVOID pUserData, DWORD dwCarID, WORD wWidth,
                                              WORD wHeight, PBYTE pbPicData, DWORD dwImgDataLen,
                                              DWORD dwRecordType, DWORD64 dw64TimeMS);
    int OnHvResultBinImage(DWORD dwCarID, WORD wWidth, WORD wHeight, PBYTE pbPicData,
                           DWORD dwImgDataLen, DWORD dwRecordType, DWORD64 dw64TimeMS);

    // 视频回调
    int OnHvCallBackH264_MP4(DWORD dwCarID, DWORD dwVideoType, DWORD dwVideoWidth,
                                DWORD dwVideoHeight, DWORD64 dw64TimeMS, PBYTE pbVideoData,
                                DWORD dwVideoDataLen, LPCSTR szVideoExtInfo);

    static int _cdecl OnHvCallBackH264Proxy(PVOID pUserData, DWORD dwCarID, DWORD dwVideoType,
                                            DWORD dwVideoWidth, DWORD dwVideoHeight,
                                            DWORD64 dw64TimeMS, PBYTE pbVideoData,
                                            DWORD dwVideoDataLen, LPCSTR szVideoExtInfo);

    int OnHvCallBackH264_AVI(DWORD dwCarID, DWORD dwVideoType, DWORD dwVideoWidth,
                             DWORD dwVideoHeight, DWORD64 dw64TimeMS, PBYTE pbVideoData,
                             DWORD dwVideoDataLen, LPCSTR szVideoExtInfo);

    int OnHvCallBackH264(DWORD dwCarID, DWORD dwVideoType, DWORD dwVideoWidth, DWORD dwVideoHeight,
                         DWORD64 dw64TimeMS, PBYTE pbVideoData, DWORD dwVideoDataLen,
                         LPCSTR szVideoExtInfo);

    /*///////////////////////////// 设备回调结束  //////////////////////////////////*/

public:
signals:
    //接收到数据时
    void OnVcrResultData(unsigned long dwCarID, int nColor, QString sPlate, qint32 vehClass,
                         int nAxisCount);
public slots:
    void OnVcrResult(QString sGuid);
    void OnHearBeat();

public:
    virtual bool StartDev();
    virtual void CloseDev();
    virtual bool LoadDriver();
    virtual void ReleaseDriver();

    // 返回设备驱动是否已加载
    bool GetDriverLoaded() { return m_bDriverLoaded; }

    QString GetVideoFileName(quint32 dwCarId);

    /*根据车牌号获取车型,
     * 先严格匹配，如果颜色对不上，再模糊匹配。查询后，此车之前的数据全部清除*/
    CVehClass GetVehClass(int nColor, const QString sPlate);

    bool GetVcrResult(int nColor, const QString &sPlate, VcrResult &vcrResult);
    bool GetLastVcrResult(VcrResult &vcrResult);
    bool GetFistVcrResult(VcrResult &vcrResult);
    void RemoveFirstCar(quint32 dwCarId = 0);
    void RemoveFirstCar(const QString &sVlp);
    bool GetVcrResult_Fuzzy(int nColor, const QString &sPlate, VcrResult &vcrResult);

    bool GetPreVcrResult(int nColor, const QString &sPlate, VcrResult &vcrResult);

    /**查询连接状态*/
    bool CheckConnectStatus();

    void ClearFrameList();

    void SetSendVideo(bool bSend);
    bool bSendVideo() { return m_bSendVideo; }

    void AddVcrResult(VcrResult &vcrResult);
    int GetVehCount();

    // 检查车牌是否在队列中且不在队首位置
    bool CheckVehPlateInQueueNotFirst(const QString &sVehPlate, QString &sError);

private:
    /*根据车牌号获取车型结果, 先严格匹配，如果颜色对不上，再模糊匹配。此车之前的数据全部清除*/
    VcrResult *GetVcrResult(int nColor, const QString sPlate);
    VcrResult *GetVcrResult_Fuzzy(int nColor, const QString &sPlate);
    //取最新的识别结果
    VcrResult *GetLastVcrResult();
    //保存上一个车型结果
    void SavePreVcrResult(VcrResult *pResult);
};

#endif  // VCRDEV_H
