#include "lanestate_etcstate.h"
#include "etclanectrl.h"
#include "ilogmsg.h"
#include "stdlog.h"
#include "dlgmain.h"

CLaneState_ETCState::CLaneState_ETCState(QObject *parent)
    : CLaneState_VehInput(CAbstractState::StateID_ETCState, parent)
{}

void CLaneState_ETCState::Enter() { CAbstractState::Enter(); }

void CLaneState_ETCState::Leave() { CAbstractState::Leave(); }

// 实现父类卡机定时器相关函数
void CLaneState_ETCState::StopCardMgrTimer(int type)
{
    // ETC状态不需要卡机定时器，提供空实现
    // if (m_timerCardMgr.isActive()) {
    //     m_timerCardMgr.stop();
    //     DebugLog("停止ETC车道卡机定时器");
    // }
}

void CLaneState_ETCState::StartCardMgrTimer()
{
    // ETC状态不需要卡机定时器，提供空实现
    // if (!m_timerCardMgr.isActive()) {
    //     m_timerCardMgr.start(1800);
    //     DebugLog("开启ETC车道卡机定时器");
    // }
}

void CLaneState_ETCState::OnCardMgrTimer()
{
    // ETC状态不需要卡机定时器，提供空实现
}

void CLaneState_ETCState::MoveMgrHead()
{
    //
}

bool CLaneState_ETCState::ProcessRsuEvent_New(int nIndex, quint32 OBUID, int nEvent,
                                              int &nErrorCode, QString &sError)
{
    bool bRlt = false;
    QString str = QTime::currentTime().toString("hhmmsszzz");
    CRsuDev *pDev = CDeviceFactory::GetRsuDev(nIndex);
    QString sFDMsg;

    if (nEvent >= CRsuDev::RsuEvent_OBUBaseInfo && nEvent <= CRsuDev::RsuEvent_IccInfo) {
        /*QString sOpStateName;

        if (!CheckAndSetOpState(opState_None, sOpStateName)) {
            GetMainDlg()->Emit_ETCShowPrompt(nIndex,
                                             QString("%1,前天线暂停交易").arg(sOpStateName));
            pDev->StopDeal(OBUID);
            return false;
        }*/
    }
    switch (nEvent) {
        case CRsuDev::RsuEvent_RsuState: {
            const CRsuBaseInfo_Jx *pRsuBaseInfo = pDev->GetRsuBaseInfo();
            for (int i = 0; i < pRsuBaseInfo->bPsamNum; ++i) {
                QString sPsam = Raw2HexStr(pRsuBaseInfo->PsamInfo[i].TerminateCode, 6);
                ShowLog(QString("天线:%1,终端机[%2]编号:%3").arg(nIndex).arg(i).arg(sPsam));
            }
            return true;
        }

        case CRsuDev::RsuEvent_OnPower: {
            ShowLog(QString("天线[%1]上电").arg(nIndex));
            return true;
            break;
        }
        case CRsuDev::RsuEvent_OBUBaseInfo: {
            if (nIndex == DevIndex_Second && CheckLastOBUPause(OBUID)) {
                GetMainDlg()->Emit_ETCShowPrompt(nIndex, QString("后天线暂停交易"));
                pDev->StopDeal(OBUID);
                return false;
            }

            if (!Ptr_ETCCtrl->IsFrontRsuWork()) {
                GetMainDlg()->Emit_ETCShowPrompt(nIndex, QString("前天线已停用"));
                pDev->StopDeal(OBUID);
                return false;
            }

            bRlt = ProcesssOBUBaseInfo_Light(nIndex, OBUID, nErrorCode, sError, sFDMsg);
            CTransInfo *pVehTranInfo = Ptr_ETCCtrl->GetCurTransInfo(nIndex, OBUID);
            if (!pVehTranInfo) {
                ErrorLog(QString("前天线交易对象为空"));
                return false;
            }
            ErrorLog(QString("天线%1,ProcessOBUBaseInfo:%2").arg(nIndex).arg(sError));
            if (bRlt) {
                pDev->ContinueDeal(OBUID);
                GetMainDlg()->Emit_ETCShowPrompt(nIndex, sError);
                ShowLog(sError);
            } else {
                if (nIndex == DevIndex_Second) {
                    PauseOBU(OBUID);
                }
                pDev->StopDeal(OBUID);
                //                pVehTranInfo->ClearTransInfo();
                DisplayTransError_ETC(nIndex, nErrorCode, sError, sFDMsg);

                if (CSpEventMgr::SpEvent_Ignore == nErrorCode || (!pVehTranInfo)) {
                    //忽略掉交易后天线干扰导致的连续天线数据
                    break;
                }
                CSpEvent spEvent = CSpEventMgr::GetSpEvent(nErrorCode, Ptr_Info->IsExitLane());

                if (spEvent.nFaileCause > 0) {
                    StdInfoLog(LogKey::OneKey_TranResult, LogKey::TranResult_TradeStatus, str,
                               QString::fromAscii(pVehTranInfo->OBUBaseInfo.szTime),
                               QString::number(2));
                    StdErrorLog(LogKey::OneKey_TranResult, LogKey::TranResult_LogicalFailCause, str,
                                QString::fromAscii(pVehTranInfo->OBUBaseInfo.szTime),
                                QString::number(spEvent.nFaileCause));
                } else {
                    StdInfoLog(LogKey::OneKey_TranResult, LogKey::TranResult_TradeStatus, str,
                               QString::fromAscii(pVehTranInfo->OBUBaseInfo.szTime),
                               QString::number(3));
                }

                pVehTranInfo->SetTransState(CTransInfo::Ts_WaitTransBegin);
            }
            break;
        }
        case CRsuDev::RsuEvent_OBUVehInfo: {
            if (!Ptr_ETCCtrl->IsFrontRsuWork()) {
                GetMainDlg()->Emit_ETCShowPrompt(nIndex, QString("天线已停用"));
                pDev->StopDeal(OBUID);
                return false;
            }
            bool bRepeat = false;
            bRlt = ProcessOBUVehInfo_Light(nIndex, OBUID, nErrorCode, sError, sFDMsg, bRepeat);
            ErrorLog(QString("ProcessOBUVehInfo:%1").arg(sError));

            if (bRlt) {
                pDev->ContinueDeal(OBUID);
                if (sError.length() > 0) this->ShowMessage(sError);
            } else {
                if (nIndex == DevIndex_Second && !bRepeat) {
                    PauseOBU(OBUID);
                }
                if (!bRepeat) pDev->StopDeal(OBUID, bRepeat);
                CTransInfo *pVehTranInfo = Ptr_ETCCtrl->GetCurTransInfo(nIndex, OBUID);
                if (!pVehTranInfo) {
                    DebugLog(QString("天线 %1 交易对象为空").arg(nIndex));
                    pDev->StopDeal(OBUID);
                    break;
                }
                //              if (pVehTranInfo) pVehTranInfo->ClearTransInfo();
                DisplayTransError_ETC(nIndex, nErrorCode, sError, sFDMsg);
                CSpEvent spEvent = CSpEventMgr::GetSpEvent(nErrorCode, Ptr_Info->IsExitLane());

                if (spEvent.nFaileCause > 0) {
                    StdInfoLog(LogKey::OneKey_TranResult, LogKey::TranResult_TradeStatus, str,
                               QString::fromAscii(pVehTranInfo->OBUBaseInfo.szTime),
                               QString::number(2));
                    StdErrorLog(LogKey::OneKey_TranResult, LogKey::TranResult_LogicalFailCause, str,
                                QString::fromAscii(pVehTranInfo->OBUBaseInfo.szTime),
                                QString::number(spEvent.nFaileCause));
                } else {
                    StdInfoLog(LogKey::OneKey_TranResult, LogKey::TranResult_TradeStatus, str,
                               QString::fromAscii(pVehTranInfo->OBUBaseInfo.szTime),
                               QString::number(3));
                }

                pVehTranInfo->SetTransState(CTransInfo::Ts_WaitTransBegin);
            }
            break;
        }
        case CRsuDev::RsuEvent_IccInfo: {
            bool bStop = false;
            if (Ptr_Info->IsEntryLane()) {
                bRlt = ProcessIccInfo_Entry(nIndex, OBUID, nErrorCode, sError, sFDMsg, bStop);
            } else {
                bRlt = ProcessIccInfo_Exit(nIndex, OBUID, nErrorCode, sError, bStop);
            }

            CTransInfo *pTranInfo = Ptr_ETCCtrl->GetCurTransInfo(nIndex, OBUID);
            if (!pTranInfo) {
                DebugLog(QString("ETC%1 交易对象为空"));
                break;
            }

            if (!bRlt) {
                if (nIndex == DevIndex_Second) {
                    PauseOBU(OBUID);
                }
                CSpEvent spEvent = CSpEventMgr::GetSpEvent(nErrorCode, Ptr_Info->IsExitLane());

                if (spEvent.nFaileCause > 0) {
                    StdInfoLog(LogKey::OneKey_TranResult, LogKey::TranResult_TradeStatus, str,
                               QString::fromAscii(pTranInfo->OBUBaseInfo.szTime),
                               QString::number(2));
                    StdInfoLog(LogKey::OneKey_TranResult, LogKey::TranResult_LogicalFailCause, str,
                               QString::fromAscii(pTranInfo->OBUBaseInfo.szTime),
                               QString::number(spEvent.nFaileCause));
                } else {
                    StdInfoLog(LogKey::OneKey_TranResult, LogKey::TranResult_TradeStatus, str,
                               QString::fromAscii(pTranInfo->OBUBaseInfo.szTime),
                               QString::number(3));
                }
            }
            ErrorLog(QString("ProcessIccInfo:%1, %2").arg(sError).arg(bRlt));

            if (!bRlt) {
                if (bStop) {
                    pDev->StopDeal(OBUID);
                    if (pTranInfo->m_bWhiteVeh) {
                        //直接放行
                        this->CompleteTransForWhiteVeh(true);
                        OnTransFinished(nIndex, true, nErrorCode, sError);
                        return true;
                    } else {
                        pTranInfo->SetTransState(CTransInfo::Ts_WaitTransBegin);
                    }
                }
                DisplayTransError_ETC(nIndex, nErrorCode, sError, sFDMsg);
            } else {
                if (sError.length() > 0) {
                    GetMainDlg()->Emit_ETCShowPrompt(nIndex, sError);
                    ShowLog(sError);
                }
            }
            break;
        }
        case CRsuDev::RsuEvent_TransResult: {
            if (Ptr_Info->IsEntryLane()) {
                bRlt = ProcessB5FrameInfo_Entry(nIndex, OBUID, nErrorCode, sError);

            } else {
                bRlt = ProcessB5FrameInfo_Exit(nIndex, OBUID, nErrorCode, sError);
            }
            OnTransFinished(nIndex, bRlt, nErrorCode, sError);
            break;
        }
        case CRsuDev::RsuEvent_B7: {
            bRlt = this->ProcessB7FrameInfo(nIndex, OBUID, nErrorCode, sError);
            break;
        }
        default:
            break;
    }
    return bRlt;
}

bool CLaneState_ETCState::ProcessOBUVehInfo_Light(int nIndex, quint32 OBUID, int &nErrorCode,
                                                  QString &sError, QString &sFDMsg, bool &bRepeat)
{
    nErrorCode = 0;
    sError.clear();
    bRepeat = false;
    DebugLog(QString("天线%1处理B3[%2]帧....").arg(nIndex).arg(OBUID));
    if (nIndex != DevIndex_First) {
        DebugLog(QString("前天线处理模块收到天线%1数据,不予处理").arg(nIndex));
        return false;
    }
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(nIndex, OBUID);
    if (!pCurTransInfo) {
        DebugLog(QString("当前车辆为空,nIndex:%1").arg(nIndex));
        sError = QString("前天线交易未检测到车辆");
        nErrorCode = CSpEventMgr::SpEvent_Other;
        return false;
    }

    CRsuDev *pRsuDev = CDeviceFactory::GetRsuDev(nIndex);
    const COBUVehInfo *pOBUVehInfo = pRsuDev->GetOBUVehInfo();
    COBUVehInfo obuVehInfo = *pOBUVehInfo;
    pOBUVehInfo = &obuVehInfo;

    CStdLog::StdLogVehInfo_OBUVehInfo(QString::fromAscii(pCurTransInfo->OBUBaseInfo.szTime),
                                      pOBUVehInfo);
    nErrorCode = 0;
    pCurTransInfo->curFrameId = 0xB3;
    pCurTransInfo->curFrameTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    if (0 != pOBUVehInfo->ErrorCode) {
        nErrorCode = CSpEventMgr::SpEvent_FrameError;
        pCurTransInfo->nErrorCode = pOBUVehInfo->ErrorCode;
        sError = QString("收到B3 帧数据错误,错误码[%1]").arg(pOBUVehInfo->ErrorCode);
        return false;
    }

    if (CTransInfo::Ts_WaitOBUVehInfo != pCurTransInfo->transState) {
        QString sState = pCurTransInfo->GetTransStateStr();
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sError = QString("%1 状态下收到B3 ,不处理").arg(sState);
        return false;
    }

    if ((0 == OBUID) || (OBUID != pCurTransInfo->dwOBUID)) {
        ErrorLog(QString("车辆信息帧，OBU 不符,OBUID1 %1 OBUID2 %2 ")
                     .arg(OBUID)
                     .arg(pCurTransInfo->dwOBUID));
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sError = "B3帧标签ID错误，不予处理";
        return false;
    }

    pCurTransInfo->SetOBUVehInfo(pOBUVehInfo, NULL);

    //

    QString sVehPlate = GB2312toUnicode(pOBUVehInfo->szVehPlate);
    QString sLog = QString("OBU 车辆信息,车型:%1 车牌：%2 车牌颜色:%3 用户类型: %4")
                       .arg(pOBUVehInfo->bVehClass)
                       .arg(sVehPlate)
                       .arg(pOBUVehInfo->nPlateColor)
                       .arg(pOBUVehInfo->bUserType);

    DebugLog(sLog);

    ShowLog(QString("前天线B3 车型:%1,用户类型:%2")
                .arg(pOBUVehInfo->bVehClass)
                .arg(pOBUVehInfo->bUserType));
    ShowLog(QString("前天线B3 车牌号:%1,车牌颜色:%2")
                .arg(QString::fromLocal8Bit(pOBUVehInfo->szVehPlate))
                .arg(pOBUVehInfo->nPlateColor));

    QString sVehClassName = GetVehClassName((CVehClass)pOBUVehInfo->bVehClass);

    if (QryTransShare(MediaType_OBU, sVehPlate, sError)) {
        return false;
    }

    char szProvider[8];
    memset(szProvider, 0, sizeof szProvider);
    memcpy(szProvider, pCurTransInfo->OBUBaseInfo.ContractProvider, 4);
    QString sOBUProvider = GB2312toUnicode(szProvider);

    CVehInfo VehInfo;
    VehInfo.Clear();
    VehInfo.PVehClass = (CVehClass)pOBUVehInfo->bVehClass;
    VehInfo.VehClass = (CVehClass)pOBUVehInfo->bVehClass;
    VehInfo.AutoVehClass = VehInfo.VehClass;
    VehInfo.GBVehType = GetGBVehTypeByUserType(pCurTransInfo->OBUVehInfo.bUserType);
    VehInfo.nVehClassWay = VehClassWay_None;

    VehInfo.nVehPlateColor = pOBUVehInfo->nPlateColor;
    if (9 == VehInfo.nVehPlateColor) {
        VehInfo.nVehPlateColor = 0;
    }
    memcpy(VehInfo.szVehPlate, pOBUVehInfo->szVehPlate, 12);

    if (QString("军车") == sOBUProvider) {
        VehInfo.GBVehType = UVT_Army;
    }

    pCurTransInfo->SetVehInfo(&VehInfo, NULL);
    /*前天线交易暂时不发送监控
    if (Ptr_Info->IsEntryLane())
        RemoteMsgMgr::GetSingleInst()->SendETCTransMsg_Entry(pCurTransInfo, 2);
    else
        RemoteMsgMgr::GetSingleInst()->SendETCTransMsg_Exit(pCurTransInfo, 2);
     */

    if (0 == sVehClassName.length()) {
        nErrorCode = CSpEventMgr::SpEvent_OBUVehClassErr;
        sError = QString("OBU车辆未知车型[%1]").arg(pOBUVehInfo->bVehClass);
        return false;
    }

    QString sPlate = GB2312toUnicode(pOBUVehInfo->szVehPlate);

    // 检查车牌是否在车型队列中且不在队首
    QString sQueueError;
    if (Ptr_ETCCtrl->CheckVehPlateInQueueNotFirst(sPlate, sQueueError)) {
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sError = sQueueError;
        DebugLog(sError);
        return false;
    }

    //出口因为疫苗车，该部分判断放在b4帧处理
    bool bYJVeh = CCardFileConverter::IsYJCard(sPlate, pOBUVehInfo->bUserType);

    if (Ptr_Info->IsEntryLane()) {
        //前天线不放行货车,出口在B4帧里处理
        if (!CheckOBUVehInfo(nIndex, pCurTransInfo, bYJVeh, nErrorCode, sError, sFDMsg)) {
            return false;
        }

        //入口发现车道内有超限车辆，也不交易
        CTransInfo *pFirstTransInfo = Ptr_ETCCtrl->RemoveTransInfoFromQue(true, false);
        if (pFirstTransInfo) {
            CVehAxisInfo axisInfo;
            quint32 dwTotalWeight = 0, dwLimit = 0;
            int nOverRate = 0;
            bool bRlt = GetCurVehWeightInfo(1, axisInfo, dwTotalWeight, dwLimit, nOverRate);
            if (bRlt) {
                if (nOverRate > 50) {
                    DebugLog(QString("发现车道内有超限%1车辆,前天线暂不交易").arg(nOverRate));
                    nErrorCode = CSpEventMgr::SpEvent_Ignore;
                    sError = QString("车道内有超限货车");
                    return false;
                }
            }
        }
        /*
        if (pOBUVehInfo->bVehClass >= VC_Car2 && pOBUVehInfo->bVehClass <= VC_Car4) {
            QDateTime curTime = QDateTime::currentDateTime();
            if (curTime.time().hour() >= 2 && curTime.time().hour() <= 4) {
                nErrorCode = CSpEventMgr::SPEvent_StopPassByTime;
                sError = QString("客2以上车辆限时通行");
                return false;
            }
        }*/

        QDateTime curTime = QDateTime::currentDateTime();
        bool bPermit = Ptr_Info->CheckVehPassPermit(VehInfo.VehClass, curTime);
        if (!bPermit) {
            QString sVehName = GetVehClassName(VehInfo.VehClass);
            DebugLog(QString("前天线交易,%1,%2 限时通行").arg(sVehName).arg(sPlate));
            nErrorCode = CSpEventMgr::SPEvent_StopPassByTime;
            sError = QString("客2以上车辆限时通行");
            return false;
        }

    } else {
    }

    // MTC后天线应该取称重信息
    CVehAxisInfo *pVehAxisInfo = NULL;
    QString sCertNo;

    pCurTransInfo->SetVehInfo(&VehInfo, pVehAxisInfo);
    pCurTransInfo->m_sCertNo = sCertNo;

    GetMainDlg()->Emit_ETCShowVehInfo(nIndex, VehInfo);
    pCurTransInfo->SetTransState(CTransInfo::Ts_WaitIccInfo);
    sError = QString("等待卡片信息...");

    bool bwhiteListVeh = CParamFileMgr::IsWhiteListVeh(sPlate);
    pCurTransInfo->SetWhiteListVeh(bwhiteListVeh);

    if (bwhiteListVeh) {
        return true;
    }
    //判断拆卸
    if (pCurTransInfo->OBUBaseInfo.OBUState.bDisassembled) {
        nErrorCode = CSpEventMgr::SpEvent_DissambleOBU;
        sError = QString("电子标签拆卸");

        if (!Ptr_Info->bAllowDisOBU()) return false;
    }
    return true;
}
