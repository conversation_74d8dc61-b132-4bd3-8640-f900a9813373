/*
 * VCR设备车型队列检查功能测试
 * 
 * 验证 VCRDev::CheckVehPlateInQueueNotFirst 方法的实现
 */

#include <iostream>
#include <string>

// 模拟VcrResult结构
struct VcrResult {
    std::string sPlate;
    int nColor;
    int vehclass;
    int dwCarID;
    
    VcrResult(const std::string& plate = "", int color = 0, int vc = 0, int carId = 0)
        : sPlate(plate), nColor(color), vehclass(vc), dwCarID(carId) {}
};

// 模拟测试VCR队列检查功能
class MockVCRDev {
private:
    std::vector<VcrResult*> m_listHvResult;
    
public:
    ~MockVCRDev() {
        for (auto* result : m_listHvResult) {
            delete result;
        }
    }
    
    void AddVcrResult(const std::string& plate, int color = 1) {
        VcrResult* result = new VcrResult(plate, color, 1, m_listHvResult.size() + 1);
        m_listHvResult.push_back(result);
    }
    
    void ClearQueue() {
        for (auto* result : m_listHvResult) {
            delete result;
        }
        m_listHvResult.clear();
    }
    
    // 实现的核心检查方法
    bool CheckVehPlateInQueueNotFirst(const std::string& sVehPlate, std::string& sError) {
        if (sVehPlate.empty()) {
            return false;
        }
        
        if (m_listHvResult.empty()) {
            return false;
        }
        
        // 检查车牌是否在队列中
        bool bFoundInQueue = false;
        int nFoundIndex = -1;
        
        for (int i = 0; i < m_listHvResult.size(); ++i) {
            VcrResult* pResult = m_listHvResult[i];
            if (pResult && !pResult->sPlate.empty() && pResult->sPlate == sVehPlate) {
                bFoundInQueue = true;
                nFoundIndex = i;
                break;
            }
        }
        
        // 如果在队列中找到了该车牌
        if (bFoundInQueue) {
            // 检查是否在队首（索引为0）
            if (nFoundIndex == 0) {
                // 在队首，允许交易
                std::cout << "车牌" << sVehPlate << "在车型设备队列队首，允许交易" << std::endl;
                return false;
            } else {
                // 不在队首，拒绝交易
                sError = "车牌" + sVehPlate + "已在车型设备队列中但不在队首位置，请等待前车通过后再进行交易";
                std::cout << "车牌" << sVehPlate << "在车型设备队列第" << (nFoundIndex + 1) << "位，拒绝交易" << std::endl;
                return true;
            }
        }
        
        // 不在队列中，允许交易
        return false;
    }
    
    void PrintQueue() {
        std::cout << "当前车型队列状态：" << std::endl;
        if (m_listHvResult.empty()) {
            std::cout << "  队列为空" << std::endl;
        } else {
            for (int i = 0; i < m_listHvResult.size(); ++i) {
                std::cout << "  [" << i << "] " << m_listHvResult[i]->sPlate << std::endl;
            }
        }
    }
};

void testVCRQueueCheck() {
    std::cout << "=== VCR设备车型队列检查功能测试 ===" << std::endl;
    
    MockVCRDev vcrDev;
    std::string error;
    
    // 测试场景1：队列为空
    std::cout << "\n--- 测试场景1：队列为空 ---" << std::endl;
    vcrDev.PrintQueue();
    bool result = vcrDev.CheckVehPlateInQueueNotFirst("苏A12345", error);
    std::cout << "检查结果：" << (result ? "拒绝交易" : "允许交易") << std::endl;
    if (result) std::cout << "错误信息：" << error << std::endl;
    
    // 测试场景2：车牌在队首
    std::cout << "\n--- 测试场景2：车牌在队首 ---" << std::endl;
    vcrDev.AddVcrResult("苏A12345");
    vcrDev.AddVcrResult("苏A67890");
    vcrDev.PrintQueue();
    result = vcrDev.CheckVehPlateInQueueNotFirst("苏A12345", error);
    std::cout << "检查结果：" << (result ? "拒绝交易" : "允许交易") << std::endl;
    if (result) std::cout << "错误信息：" << error << std::endl;
    
    // 测试场景3：车牌不在队首
    std::cout << "\n--- 测试场景3：车牌不在队首 ---" << std::endl;
    vcrDev.PrintQueue();
    result = vcrDev.CheckVehPlateInQueueNotFirst("苏A67890", error);
    std::cout << "检查结果：" << (result ? "拒绝交易" : "允许交易") << std::endl;
    if (result) std::cout << "错误信息：" << error << std::endl;
    
    // 测试场景4：车牌不在队列中
    std::cout << "\n--- 测试场景4：车牌不在队列中 ---" << std::endl;
    vcrDev.PrintQueue();
    result = vcrDev.CheckVehPlateInQueueNotFirst("苏B11111", error);
    std::cout << "检查结果：" << (result ? "拒绝交易" : "允许交易") << std::endl;
    if (result) std::cout << "错误信息：" << error << std::endl;
    
    // 测试场景5：空车牌
    std::cout << "\n--- 测试场景5：空车牌 ---" << std::endl;
    vcrDev.PrintQueue();
    result = vcrDev.CheckVehPlateInQueueNotFirst("", error);
    std::cout << "检查结果：" << (result ? "拒绝交易" : "允许交易") << std::endl;
    if (result) std::cout << "错误信息：" << error << std::endl;
    
    // 测试场景6：多车牌队列中的第三位
    std::cout << "\n--- 测试场景6：多车牌队列中的第三位 ---" << std::endl;
    vcrDev.ClearQueue();
    vcrDev.AddVcrResult("苏A11111");
    vcrDev.AddVcrResult("苏A22222");
    vcrDev.AddVcrResult("苏A33333");
    vcrDev.AddVcrResult("苏A44444");
    vcrDev.PrintQueue();
    result = vcrDev.CheckVehPlateInQueueNotFirst("苏A33333", error);
    std::cout << "检查结果：" << (result ? "拒绝交易" : "允许交易") << std::endl;
    if (result) std::cout << "错误信息：" << error << std::endl;
}

int main() {
    testVCRQueueCheck();
    return 0;
}

/*
预期输出：
=== VCR设备车型队列检查功能测试 ===

--- 测试场景1：队列为空 ---
当前车型队列状态：
  队列为空
检查结果：允许交易

--- 测试场景2：车牌在队首 ---
当前车型队列状态：
  [0] 苏A12345
  [1] 苏A67890
车牌苏A12345在车型设备队列队首，允许交易
检查结果：允许交易

--- 测试场景3：车牌不在队首 ---
当前车型队列状态：
  [0] 苏A12345
  [1] 苏A67890
车牌苏A67890在车型设备队列第2位，拒绝交易
检查结果：拒绝交易
错误信息：车牌苏A67890已在车型设备队列中但不在队首位置，请等待前车通过后再进行交易

--- 测试场景4：车牌不在队列中 ---
当前车型队列状态：
  [0] 苏A12345
  [1] 苏A67890
检查结果：允许交易

--- 测试场景5：空车牌 ---
当前车型队列状态：
  [0] 苏A12345
  [1] 苏A67890
检查结果：允许交易

--- 测试场景6：多车牌队列中的第三位 ---
当前车型队列状态：
  [0] 苏A11111
  [1] 苏A22222
  [2] 苏A33333
  [3] 苏A44444
车牌苏A33333在车型设备队列第3位，拒绝交易
检查结果：拒绝交易
错误信息：车牌苏A33333已在车型设备队列中但不在队首位置，请等待前车通过后再进行交易
*/
