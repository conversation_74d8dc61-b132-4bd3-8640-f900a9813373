#include "vcrdev.h"

#include <cstring>
#include <string>
#include <fstream>

#include "globalutils.h"
#include "ilogmsg.h"
#include "lanetype.h"
#include "tool_func.h"
#include "vehplatefunc.h"

using std::string;

#define MAX_RESULT_COUNT 20

/* CALL BACK TYPE回调数据类型 */
#define CALLBACK_TYPE_RECORD_PLATE 0xFFFF0001       /*车牌信息回调*/
#define CALLBACK_TYPE_RECORD_BIGIMAGE 0xFFFF0002    /*识别结果大图回调*/
#define CALLBACK_TYPE_RECORD_SMALLIMAGE 0xFFFF0003  /*小图回调*/
#define CALLBACK_TYPE_RECORD_BINARYIMAGE 0xFFFF0004 /*二进制*/
#define CALLBACK_TYPE_RECORD_INFOBEGIN 0xFFFF0005   /*识别开始*/
#define CALLBACK_TYPE_RECORD_INFOEND 0xFFFF0006     /*识别结束*/
#define CALLBACK_TYPE_STRING 0xFFFF0007             /*统计信息*/
#define CALLBACK_TYPE_JPEG_FRAME 0xFFFF0008
#define CALLBACK_TYPE_H264_VIDEO 0xFFFF0009
#define CALLBACK_TYPE_HISTORY_VIDEO 0xFFFF0010
#define CALLBACK_TYPE_TFD_STRING 0xFFFF0011
#define CALLBACK_TYPE_RECORD_ILLEGALVIDEO 0xFFFF0018

/* BIG Image 识别结果大图类型定义 */
#define RECORD_BIGIMG_BEST_SNAPSHOT 0x0001 /**< 最清晰识别图 */
#define RECORD_BIGIMG_LAST_SNAPSHOT 0x0002 /**< 最后识别图 */
#define RECORD_BIGIMG_BEGIN_CAPTURE 0x0003 /**< 开始抓拍图 */ 车头
#define RECORD_BIGIMG_BEST_CAPTURE 0x0004 /**< 最清晰抓拍图 */ 车身
#define RECORD_BIGIMG_LAST_CAPTURE 0x0005 /**<  最后抓拍图 */ 车尾
#define RECORD_BIGIMG_PLATE 0x0006     /**< 车牌小图 */
#define RECORD_BIGIMG_PLATE_BIN 0x0007 /**< 车牌二值图 */

/*video Type*/
#define VIDEO_TYPE_UNKNOWN 0xffff0200
#define VIDEO_TYPE_H264_NORMAL_I 0xffff0201
#define VIDEO_TYPE_H264_NORMAL_P 0xffff0202
#define VIDEO_TYPE_H264_HISTORY_I 0xffff0203
#define VIDEO_TYPE_H264_HISTORY_P 0xffff0205
#define VIDEO_TYPE_JPEG_HISTORY 0xffff0204
#define VIDEO_TYPE_MP4 0xffff0210 /**< 顺一动态库用 */

/* H264标志*/
#define H264_FLAG_INVAIL 0xffff0800      /**< 无效视频数据 */
#define H264_FLAG_VAIL 0xffff0801        /**< 有效视频数据 */
#define H264_FLAG_HISTROY_END 0xffff0802 /**< 历史结果结束标志 */

/* 连接类型定义 */
#define CONN_TYPE_UNKNOWN 0xffff0000 /**< 未知 */
#define CONN_TYPE_IMAGE 0xffff0001   /**< 图片 */
#define CONN_TYPE_VIDEO 0xffff0002   /**< 视频 */
#define CONN_TYPE_RECORD 0xffff0003  /**< 识别结果 */

/* 连接状态 */
#define CONN_STATUS_UNKNOWN 0xffff0400  /**< 未知 */
#define CONN_STATUS_NORMAL 0xffff0401   /**< 正常 */
#define CONN_STATUS_DISCONN 0xffff0402  /**< 断开 */
#define CONN_STATUS_RECONN 0xffff0403   /**< 重连中 */
#define CONN_STATUS_RECVDONE 0xffff0404 /**< 历史数据接收完成 */
//以下两种状态为主动连接特有
#define CONN_STATUS_CONNFIRST 0xffff0405    /**< 初始连接*/
#define CONN_STATUS_CONNOVERTIME 0xffff0406 /**< 连接超时*/

/**
@name    GetDataFromAppenedInfo
@brief   从附加信息中获取某项的数据
@param[in]        char * pszAppendInfo  附加信息
@param[in]        string strItemName    项名,如<RoadNumber value="0" chnname="车道" />中的RoadNumber
@param[in]        char * pszRstBuf      存储获取到的数据缓存区
@param[in]        int *piRstBufLen      缓冲区pszRstBuf的长度
@return           成功true,失败false
*/
bool GetDataFromAppenedInfo(const char *pszAppendInfo, string strItemName, char *pszRstBuf,
                            int *piRstBufLen)
{
    // if (pszAppendInfo == NULL || pszRstBuf == NULL || piRstBufLen == NULL || *piRstBufLen <= 0)
    //{
    //    return false;
    //}

    // <RoadNumber value="0" chnname="车道" />
    // <StreetName value="" chnname="路口名称" />
    string strAppendInfo = pszAppendInfo;
    size_t siStart = strAppendInfo.find(strItemName);
    if (siStart == string::npos) {
        return false;
    }
    siStart = strAppendInfo.find("\"", siStart + 1);
    if (siStart == string::npos) {
        return false;
    }
    size_t siEnd = strAppendInfo.find("\"", siStart + 1);
    if (siEnd == string::npos) {
        return false;
    }

    string strRst = strAppendInfo.substr(siStart + 1, siEnd - siStart - 1);
    if (*piRstBufLen < (int)strRst.length()) {
        *piRstBufLen = (int)strRst.length();
        return false;
    }

    strncpy(pszRstBuf, strRst.c_str(), (int)strRst.length());
    *piRstBufLen = (int)strRst.length();
    return true;
}

bool VCRDev::m_bDriverLoaded = false;
QLibrary VCRDev::m_hLibModule;

/**
 * @brief			打开设备句柄
 * @param[in]		szIp			设备的IP地址
 * @param[in]		szApiVer		对应设备的API版本。注：为NULL则默认HVAPI_API_VERSION
 * @return			成功：设备句柄；失败：NULL
 */
typedef PVOID(CDECL *Func_HVAPI_OpenEx)(LPCSTR szIp, LPCSTR szApiVer);

/**
 * @brief			关闭设备句柄
 * @param[in]		hHandle			对应设备的有效句柄
 * @return			成功：S_OK；失败：E_FAIL
 */
typedef HRESULT(CDECL *Func_HVAPI_CloseEx)(PVOID hHandle);

/**
 * @brief			设置数据流接收回调函数
 * @param[in]		hHandle			对应设备的有效句柄
 * @param[in]		pFunc
 * 回调函数指针。注：为NULL时表示关闭nStreamType类型的数据流。
 * @param[in]		pUserData		传入回调函数的用户数据指针
 * @param[in]		iVideoID		视频通道，目前只使用0
 * @param[in]		nStreamType		回调数据流类型
 * @param[in]		szConnCmd		数据流连接命令
 * @return			成功：S_OK；失败：E_FAIL
 */
typedef HRESULT(CDECL *Func_HVAPI_SetCallBackEx)(PVOID hHandle, PVOID pFunc, PVOID pUserData,
                                                 INT iVideoID, INT iCallBackType, LPCSTR szConnCmd);

/**
 * @brief			获取连接状态
 * @param[in]		hHandle			对应设备的有效句柄
 * @param[in]		nStreamType		数据流类型（即：连接类型）
 * @param[out]		pdwConnStatus	该连接的当前状态
 * @return			成功：S_OK；失败：E_FAIL
 */
typedef HRESULT(CDECL *Func_HVAPI_GetConnStatusEx)(PVOID hHandle, INT nStreamType,
                                                   DWORD *pdwConnStatus);

Func_HVAPI_OpenEx HVAPI_OpenEx = NULL;
Func_HVAPI_CloseEx HVAPI_CloseEx = NULL;
Func_HVAPI_SetCallBackEx HVAPI_SetCallBackEx = NULL;
Func_HVAPI_GetConnStatusEx HVAPI_GetConnStatusEx = NULL;

VCRDev::VCRDev()
{
    m_hDevice = 0;
    m_pcurHvResult = NULL;
    m_bSendVideo = false;

    m_pHttpListener = NULL;
    m_pRequestHandler = NULL;
}

VCRDev::~VCRDev()
{
    if (m_hDevice) {
        CloseDev();
    }
    if (m_bDriverLoaded) {
        ReleaseDriver();
    }

    while (m_listHvResult.size() > 0) {
        VcrResult *pResult = m_listHvResult.front();
        m_listHvResult.pop_front();
        if (pResult) {
            pResult->RemoveImgFile();
            SafeDelMemory(pResult, false);
        }
    }

    while (m_preResult.size() > 0) {
        VcrResult *pResult = m_preResult.front();
        m_preResult.removeFirst();
        if (pResult) {
            pResult->RemoveImgFile();
            SafeDelMemory(pResult, false);
        }
    }
}

int VCRDev::OnHvResultBeginProxy(PVOID pUserData, DWORD dwCarID)
{
    DebugLog("BeginProxy");
    if (pUserData == NULL) {
        return 0;
    }
    VCRDev *pThis = (VCRDev *)pUserData;
    return pThis->OnHvResultBegin(dwCarID);
}

int VCRDev::OnHvResultEndProxy(PVOID pUserData, DWORD dwCarID)
{
    if (pUserData == NULL) {
        return 0;
    }
    VCRDev *pThis = (VCRDev *)pUserData;
    return pThis->OnHvResultEnd(dwCarID);
}
int VCRDev::OnHvResultPlateProxy(PVOID pUserData, DWORD dwCarID, LPCSTR pcPlateNo,
                                 LPCSTR pcAppendInfo, DWORD dwRecordType, DWORD64 dw64TimeMS)
{
    if (pUserData == NULL) {
        return 0;
    }
    VCRDev *pThis = (VCRDev *)pUserData;
    return pThis->OnHvResultPlate(dwCarID, pcPlateNo, pcAppendInfo, dwRecordType, dw64TimeMS);
}
int VCRDev::OnHvResultBigImageProxy(PVOID pUserData, DWORD dwCarID, WORD wImgType, WORD wWidth,
                                    WORD wHeight, PBYTE pbPicData, DWORD dwImgDataLen,
                                    DWORD dwRecordType, DWORD64 dw64TimeMS)
{
    if (pUserData == NULL) {
        return 0;
    }
    VCRDev *pThis = (VCRDev *)pUserData;
    return pThis->OnHvResultBigImage(dwCarID, wImgType, wWidth, wHeight, pbPicData, dwImgDataLen,
                                     dwRecordType, dw64TimeMS);
}
int VCRDev::OnHvResultSmallImageProxy(PVOID pUserData, DWORD dwCarID, WORD wWidth, WORD wHeight,
                                      PBYTE pbPicData, DWORD dwImgDataLen, DWORD dwRecordType,
                                      DWORD64 dw64TimeMS)
{
    if (pUserData == NULL) {
        return 0;
    }
    VCRDev *pThis = (VCRDev *)pUserData;
    return pThis->OnHvResultSmallImage(dwCarID, wWidth, wHeight, pbPicData, dwImgDataLen,
                                       dwRecordType, dw64TimeMS);
}
int VCRDev::OnHvResultBinImageProxy(PVOID pUserData, DWORD dwCarID, WORD wWidth, WORD wHeight,
                                    PBYTE pbPicData, DWORD dwImgDataLen, DWORD dwRecordType,
                                    DWORD64 dw64TimeMS)
{
    if (pUserData == NULL) {
        return 0;
    }
    VCRDev *pThis = (VCRDev *)pUserData;
    return pThis->OnHvResultBinImage(dwCarID, wWidth, wHeight, pbPicData, dwImgDataLen,
                                     dwRecordType, dw64TimeMS);
}

/**
@name    OnHvResultBegin
@brief   结果回调开始
@param[in]        DWORD dwCarID
@return           int
*/
int VCRDev::OnHvResultBegin(DWORD dwCarID)
{
    DebugLog(QString("OnHvResultBegin - 开始%1").arg(dwCarID));
    if (m_pcurHvResult != NULL) {
        SafeDelMemory(m_pcurHvResult, false);
    }
    m_pcurHvResult = new VcrResult();
    if (m_pcurHvResult != NULL) {
        m_pcurHvResult->dwCarID = dwCarID;
    }
    DebugLog("OnHvResultBegin - 结束");
    return 0;
}

int VCRDev::OnHvResultEnd(DWORD dwCarID)
{
    DebugLog("OnHvResultEnd -- 开始");
    if (m_pcurHvResult != NULL && m_pcurHvResult->dwCarID == dwCarID) {
        //取出识别结果中的信息
        int nColor = m_pcurHvResult->nColor;
        QString sPlate = m_pcurHvResult->sPlate;
        CVehClass vc = m_pcurHvResult->vehclass;
        int axis = m_pcurHvResult->nAxleCnt;

        m_LockResult.lock();
        //将结果加入队列
        VcrResult *pResult = NULL;
        if (m_listHvResult.size() > 0) pResult = m_listHvResult.last();
        if (pResult) {
            if (m_pcurHvResult->dwCarID == pResult->dwCarID) {
                m_listHvResult.pop_back();
                pResult->RemoveImgFile();
                SafeDelMemory(pResult, false);
            }
        }
        m_listHvResult.push_back(m_pcurHvResult);
        DebugLog(QString("车型识别结束,%1_%2,车型:%3,轴数:%4")
                     .arg(sPlate)
                     .arg(nColor)
                     .arg(vc)
                     .arg(axis));
        // 结果由处理者释放
        m_pcurHvResult = NULL;
        // 判断数据的数量
        while (m_listHvResult.size() > MAX_RESULT_COUNT) {
            VcrResult *pResult = m_listHvResult.front();
            m_listHvResult.pop_front();
            if (pResult) {
                pResult->RemoveImgFile();
                SafeDelMemory(pResult, false);
            }
        }
        m_LockResult.unlock();
        DebugLog(QString("OnHvResultEnd 开始发通知, color:%1,Plate %2,VC:%3")
                     .arg(nColor)
                     .arg(sPlate)
                     .arg(vc));
        //接收到数据, 发出通知
        emit OnVcrResultData(dwCarID, nColor, sPlate, vc, axis);
    }
    DebugLog("OnHvResultEnd --  结束");
    return 0;
}

/**
 * @brief TransVehPlateColor 判断车牌颜色
 * @param sPlate 格式为“车牌颜色（单个汉字）+车牌号”，如“蓝贵A12345”，无车牌的情况下，输出为 “无车牌
 * @return
 *
 * 2)当前我们设备输出的车牌颜色都是单字，如：蓝、黄、白、黑、绿。
 * 3)普通车牌根据颜色汉字判断即可，新能源牌判断比较特殊，规则如下：
 * a)黄绿新能源牌： 车牌带有“绿”，且最后一位为“D”或“F”，如“绿粤B12345D”。
 * b)存在D或F，且在车牌号第三位，则认为该车牌是渐变绿新能源牌，如“绿粤BD12345”。
 * c)若不满足上述的条件，则认为是普通绿牌。
 */
quint8 TransVPColor(QString &sPlate)
{
    QString sColor = sPlate.left(2);
    if (0 == sColor.compare("渐绿")) {
        sPlate = sPlate.mid(2);
        return VP_COLOR_LittleGREEN;
    } else if (0 == sColor.compare("黄绿")) {
        sPlate = sPlate.mid(2);
        return VP_COLOR_YELLOWGREEN;
    } else if (0 == sColor.compare(QString("蓝白"))) {
        sPlate = sPlate.mid(2);
        return VP_COLOR_BLUEWHITE;
    }

    sColor = sPlate.left(1);
    if (0 == sColor.compare("蓝")) {
        sPlate = sPlate.mid(1);
        return VP_COLOR_BLUE;
    } else if (0 == sColor.compare("黄")) {
        sPlate = sPlate.mid(1);
        return VP_COLOR_YELLOW;
    } else if (0 == sColor.compare("白")) {
        sPlate = sPlate.mid(1);
        return VP_COLOR_WHITE;
    } else if (0 == sColor.compare("黑")) {
        QString stmpPlate = sPlate.mid(1);
        sPlate = stmpPlate;
        return VP_COLOR_BLACK;
    }

    if (0 == sColor.compare("绿")) {
        /*
         * 3)普通车牌根据颜色汉字判断即可，新能源牌判断比较特殊，规则如下：
         * a)黄绿新能源牌： 车牌带有“绿”，且最后一位为“D”或“F”，如“绿粤B12345D”。
         * b)存在D或F，且在车牌号第三位，则认为该车牌是渐变绿新能源牌，如“绿粤BD12345”。
         * */
        if (sPlate.length() < 7) return VP_COLOR_GREEN;

        QString sLetterInPlate = QString("DABCEFGHJK");
        QString sLetter = sPlate.right(1);
        if (sLetterInPlate.indexOf(sLetter) >= 0) {
            sPlate = sPlate.mid(1);
            return VP_COLOR_YELLOWGREEN;
        } else {
            if (sLetterInPlate.indexOf(sPlate.at(3)) >= 0) {
                sPlate = sPlate.mid(1);
                return VP_COLOR_LittleGREEN;
            } else {
                sPlate = sPlate.mid(1);
                return VP_COLOR_GREEN;
            }
        }

        /*
                if (sPlate.right(1).compare("D") == 0 || sPlate.right(1).compare("F") == 0) {
                    return VP_COLOR_YELLOWGREEN;
                } else if (sPlate.indexOf("D") == 3 || sPlate.indexOf("F") == 3) {
                    return VP_COLOR_LittleGREEN;
                } else {
                    return VP_COLOR_GREEN;
                }
         */
    }

    if (0 == sColor.compare("红")) {
        sPlate = sPlate.mid(1);
        return VP_COLOR_RED;
    }
    return VP_COLOR_NONE;
}

CVehClass TransVehClass(const QString sVehClass)
{
    if (sVehClass.compare("客1") == 0) {
        return VC_Car1;
    } else if (sVehClass.compare("客2") == 0) {
        return VC_Car2;
    } else if (sVehClass.compare("客3") == 0) {
        return VC_Car3;
    } else if (sVehClass.compare("客4") == 0) {
        return VC_Car4;
    } else if (sVehClass.compare("货1") == 0) {
        return VC_Truck1;
    } else if (sVehClass.compare("货2") == 0) {
        return VC_Truck2;
    } else if (sVehClass.compare("货3") == 0) {
        return VC_Truck3;
    } else if (sVehClass.compare("货4") == 0) {
        return VC_Truck4;
    } else if (sVehClass.compare("货5") == 0) {
        return VC_Truck5;
    } else if (sVehClass.compare("货6") == 0) {
        return VC_Truck6;
    } else {
        return VC_None;
    }
}

int VCRDev::OnHvResultPlate(DWORD dwCarID, LPCSTR pcPlateNo, LPCSTR pcAppendInfo,
                            DWORD dwRecordType, DWORD64 dw64TimeMS)
{
    DebugLog(QString("OnHvResultPlate -- begin(carId:%1,recordtype:%2,dw64TimeMs:%3)")
                 .arg(dwCarID)
                 .arg(dwRecordType)
                 .arg(dw64TimeMS));
    if (m_pcurHvResult == NULL) {
        DebugLog("OnHvResultPlate -- 1");
        return -1;
    }

    if (0 == dw64TimeMS)
        m_pcurHvResult->dtPlate = QDateTime::currentDateTime();
    else
        m_pcurHvResult->dtPlate.setMSecsSinceEpoch(dw64TimeMS);
    // 车牌号
    if (pcPlateNo != NULL) {
        QString sPlate = GB2312toUnicode(pcPlateNo);
        DebugLog(QString("车型识别返回原始车牌:%1").arg(sPlate));
        char szVehPlate[16];  // MAX_VEHPLATE_LEN + 1];
        memset(szVehPlate, 0, sizeof szVehPlate);
        RemovePlateSpecChar(szVehPlate, sizeof szVehPlate, (const char *)pcPlateNo, 16);

        sPlate = GB2312toUnicode(szVehPlate);

        if (sPlate.length() <= 7 || sPlate.indexOf("无") != -1)  // 无车牌
        {
            m_pcurHvResult->sPlate = "无车牌";
            m_pcurHvResult->nColor = VP_COLOR_UNKNOW;
        } else {
            //判断车牌颜色
            m_pcurHvResult->nColor = TransVPColor(sPlate);
            // 车牌号码
            QByteArray bPlate = sPlate.toLocal8Bit();

            if (bPlate.size() > 12) {
                while (1) {
                    sPlate = sPlate.left(sPlate.length() - 1);
                    bPlate = sPlate.toLocal8Bit();
                    if (bPlate.size() <= 12) break;
                }
            }
            m_pcurHvResult->sPlate = sPlate;

            bool b = IsValidVehPlate(m_pcurHvResult->sPlate);
            if (b) {
                m_pcurHvResult->bPlateValid = 1;
            } else
                m_pcurHvResult->bPlateValid = 0;
        }
        DebugLog(QString("车型设备返回车牌:%1_%2,有效值:%3")
                     .arg(m_pcurHvResult->sPlate)
                     .arg(m_pcurHvResult->nColor)
                     .arg(m_pcurHvResult->bPlateValid));
    }

    char chTemp[256] = {0};
    int iLenth = 256;

    // 附加信息, 解析车型及轴数
    if (pcAppendInfo != NULL) {
        // QString sAppendInfo = QString::fromLocal8Bit(pcAppendInfo);
        // DebugLog(QString("AppendInfo:%1").arg(sAppendInfo));

        if (GetDataFromAppenedInfo(pcAppendInfo, "VehicleType", chTemp, &iLenth)) {
            m_pcurHvResult->vehclass = TransVehClass(GB2312toUnicode(chTemp));
            DebugLog(QString("车型设备返回车型:%1,%2")
                         .arg(GB2312toUnicode(chTemp))
                         .arg(m_pcurHvResult->vehclass));
        }
        memset(chTemp, 0, sizeof(chTemp));
        m_pcurHvResult->nAxleCnt = atoi(chTemp);
        if (GetDataFromAppenedInfo(pcAppendInfo, "AxleCnt", chTemp, &iLenth)) {
            DebugLog(QString("车型设备返回车轴:%1").arg(chTemp));
            m_pcurHvResult->nAxleCnt = atoi(chTemp);
        }
    }
    DebugLog("OnHvResultPlate -- end");
    return 0;
}

int VCRDev::OnHvResultBigImage(DWORD dwCarID, WORD wImgType, WORD wWidth, WORD wHeight,
                               PBYTE pbPicData, DWORD dwImgDataLen, DWORD dwRecordType,
                               DWORD64 dw64TimeMS)
{
    DebugLog(QString("OnHvResultBigImage -- begin,ImageLen:%1,CarId:%2,imgType:%3,recordType:%4")
                 .arg(dwImgDataLen)
                 .arg(dwCarID)
                 .arg(wImgType)
                 .arg(dwRecordType));
    if (m_pcurHvResult == NULL || pbPicData == NULL || dwImgDataLen <= 0) {
        DebugLog("OnHvResultBigImage -- 1");
        return -1;
    }

    int iImgIndex = wImgType;     // m_pcurHvResult->iBigImgCount;
    if (iImgIndex >= VehImg_End)  // 大图数组索引返回0到5
    {
        DebugLog(QString("OnHvResultBigImage,Error ImgType:%1").arg(iImgIndex));
        return -1;
    }
    /*
    SafeDelMemory(m_pcurHvResult->bigImage[iImgIndex].pbImgData, true);
    m_pcurHvResult->bigImage[iImgIndex].pbImgData = new BYTE[dwImgDataLen];
    if (m_pcurHvResult->bigImage[iImgIndex].pbImgData == NULL) {
        DebugLog("OnHvResultBigImage -- 3");
        return -1;
    }
    memset(m_pcurHvResult->bigImage[iImgIndex].pbImgData, 0, dwImgDataLen);
    memcpy(m_pcurHvResult->bigImage[iImgIndex].pbImgData, pbPicData, dwImgDataLen);
    */
    m_pcurHvResult->bigImage[iImgIndex].wImgType = wImgType;
    m_pcurHvResult->bigImage[iImgIndex].iImgWidth = wWidth;
    m_pcurHvResult->bigImage[iImgIndex].iImgHeight = wHeight;
    m_pcurHvResult->bigImage[iImgIndex].iImgSize = dwImgDataLen;
    m_pcurHvResult->bigImage[iImgIndex].dw64TimeMS = dw64TimeMS;
    m_pcurHvResult->iBigImgCount++;  // 大图数量加1
    //图像存盘
    QString sTime = QDateTime::currentDateTime().toString("hhmmsszzz");
    m_pcurHvResult->bigImage[iImgIndex].sImgFile =
        QString("%1bigimg%2_%3_%4.jpg").arg(m_sImagePath).arg(dwCarID).arg(iImgIndex).arg(sTime);
    WriteBufToFile(m_pcurHvResult->bigImage[iImgIndex].sImgFile.toLocal8Bit().data(), pbPicData,
                   dwImgDataLen);
    DebugLog(
        QString("OnHvResultBigImage:%1 -- end").arg(m_pcurHvResult->bigImage[iImgIndex].sImgFile));
    return 0;
}

int VCRDev::OnHvResultSmallImage(DWORD dwCarID, WORD wWidth, WORD wHeight, PBYTE pbPicData,
                                 DWORD dwImgDataLen, DWORD dwRecordType, DWORD64 dw64TimeMS)
{
    DebugLog(QString("OnHvResultSmallImage -- begin,ImageLen:%1").arg(dwImgDataLen));
    if (m_pcurHvResult == NULL || pbPicData == NULL || dwImgDataLen <= 0) {
        DebugLog("OnHvResultSmallImage -- 1");
        return -1;
    }

    //  SafeDelMemory(m_pcurHvResult->smallImage.pbImgData, true);
    /*
    m_pcurHvResult->smallImage.pbImgData = new BYTE[dwImgDataLen];
    if (m_pcurHvResult->smallImage.pbImgData == NULL) {
        DebugLog("OnHvResultSmallImage -- 2");
        return -1;
    }
    memset(m_pcurHvResult->smallImage.pbImgData, 0, dwImgDataLen);
    memcpy(m_pcurHvResult->smallImage.pbImgData, pbPicData, dwImgDataLen);*/
    m_pcurHvResult->smallImage.iImgSize = dwImgDataLen;
    m_pcurHvResult->smallImage.iImgWidth = wWidth;
    m_pcurHvResult->smallImage.iImgHeight = wHeight;
    m_pcurHvResult->smallImage.dw64TimeMS = dw64TimeMS;
    m_pcurHvResult->smallImage.wImgType = RECORD_BIGIMG_PLATE;
    //图像存盘
    m_pcurHvResult->smallImage.sImgFile =
        QString("%1smallimg%2.jpg").arg(m_sImagePath).arg(dwCarID);
    WriteBufToFile(m_pcurHvResult->smallImage.sImgFile.toLatin1().data(), pbPicData, dwImgDataLen);
    DebugLog("OnHvResultSmallImage -- end");
    return 0;
}

int VCRDev::OnHvResultBinImage(DWORD dwCarID, WORD wWidth, WORD wHeight, PBYTE pbPicData,
                               DWORD dwImgDataLen, DWORD dwRecordType, DWORD64 dw64TimeMS)
{
    DebugLog(QString("OnHvResultBinImage -- begin,ImageLen:%1").arg(dwImgDataLen));
    if (m_pcurHvResult == NULL || pbPicData == NULL || dwImgDataLen <= 0) {
        DebugLog("OnHvResultBinImage -- 1");
        return -1;
    }

    /*
    m_pcurHvResult->binImage.pbImgData = new BYTE[dwImgDataLen];
    if (m_pcurHvResult->binImage.pbImgData == NULL) {
        DebugLog("OnHvResultBinImage -- 2");
        return -1;
    }
    memset(m_pcurHvResult->binImage.pbImgData, 0, dwImgDataLen);
    memcpy(m_pcurHvResult->binImage.pbImgData, pbPicData, dwImgDataLen);*/
    m_pcurHvResult->binImage.iImgSize = dwImgDataLen;
    m_pcurHvResult->binImage.iImgWidth = wWidth;
    m_pcurHvResult->binImage.iImgHeight = wHeight;
    m_pcurHvResult->binImage.dw64TimeMS = dw64TimeMS;
    m_pcurHvResult->binImage.wImgType = RECORD_BIGIMG_PLATE_BIN;
    //图像存盘
    m_pcurHvResult->binImage.sImgFile = QString("%1binimg%2.jpg").arg(m_sImagePath).arg(dwCarID);
    WriteBufToFile(m_pcurHvResult->binImage.sImgFile.toLatin1().data(), pbPicData, dwImgDataLen);

    DebugLog("OnHvResultBinImage -- end");
    return 0;
}

int VCRDev::OnHvCallBackH264_MP4(DWORD dwCarID, DWORD dwVideoType, DWORD dwVideoWidth,
                                DWORD dwVideoHeight, DWORD64 dw64TimeMS, PBYTE pbVideoData,
                                DWORD dwVideoDataLen, LPCSTR szVideoExtInfo)
{
    QString sFileName = QString("%1_%2.mp4")
                                        .arg(dwCarID)
                                        .arg(QDateTime::currentDateTime().toString("yyyyMMddhhmmss"));

    sFileName = m_sImagePath + sFileName;
    std::ofstream outFile(sFileName.toLocal8Bit().constData(), std::ios::binary);
    if (!outFile) {
        ErrorLog(QString("无法创建MP4文件: %1").arg(sFileName));
        return 0;
    }
    
    outFile.write(reinterpret_cast<const char*>(pbVideoData), dwVideoDataLen);
    outFile.close();
    
    DebugLog(QString("已保存视频帧到文件: %1").arg(sFileName));

    VideoCarIdInfo carInfo;
    carInfo.dwCarId = dwCarID;
    carInfo.sVideoFileName = sFileName;

    {
        QMutexLocker locker(&m_cardIdMutex);
        while (m_CarIdList.size() > 5) {
            VideoCarIdInfo videoInfo = m_CarIdList.front();
            m_CarIdList.pop_front();

            if (videoInfo.sVideoFileName.length() > 0)
                RemoveFile(videoInfo.sVideoFileName);
        }
        m_CarIdList.push_back(carInfo);
    }

    return 0;
}

int VCRDev::OnHvCallBackH264Proxy(PVOID pUserData, DWORD dwCarID, DWORD dwVideoType,
                                  DWORD dwVideoWidth, DWORD dwVideoHeight, DWORD64 dw64TimeMS,
                                  PBYTE pbVideoData, DWORD dwVideoDataLen, LPCSTR szVideoExtInfo)
{
    if (!pUserData) return 0;
    VCRDev *pDev = (VCRDev *)pUserData;
    if(dwVideoType == VIDEO_TYPE_MP4){
        return pDev->OnHvCallBackH264_MP4(dwCarID, dwVideoType, dwVideoWidth, dwVideoHeight, dw64TimeMS,
                                      pbVideoData, dwVideoDataLen, szVideoExtInfo);
    }else{
        return pDev->OnHvCallBackH264_AVI(dwCarID, dwVideoType, dwVideoWidth, dwVideoHeight, dw64TimeMS,
                                      pbVideoData, dwVideoDataLen, szVideoExtInfo);
    }
}

int VCRDev::OnHvCallBackH264_AVI(DWORD dwCarID, DWORD dwVideoType, DWORD dwVideoWidth,
                                 DWORD dwVideoHeight, DWORD64 dw64TimeMS, PBYTE pbVideoData,
                                 DWORD dwVideoDataLen, LPCSTR szVideoExtInfo)
{
    QString sFrameType;
    switch (dwVideoType) {
        case VIDEO_TYPE_H264_HISTORY_I:
            sFrameType = QString("I_Frame");
            break;
        case VIDEO_TYPE_H264_HISTORY_P:
            sFrameType = QString("P_Frame");
            break;
        default:
            sFrameType = QString("UNKNOW Frame");
            break;
    }

    // DebugLog(QString("On H264,CardId:%1,sFrameType:%2,videoType:%3")
    //              .arg(dwCarID)
    //            .arg(sFrameType)
    //           .arg(dwVideoType));

    if (dwVideoType == VIDEO_TYPE_H264_HISTORY_I || VIDEO_TYPE_H264_HISTORY_P == dwVideoType) {
        if (dwCarID != H264_FLAG_HISTROY_END) {
            CustH264Struct *pFrame = new CustH264Struct();
            pFrame->m_pbH264FrameData = new unsigned char[dwVideoDataLen];
            if (pFrame->m_pbH264FrameData) {
                memset(pFrame->m_pbH264FrameData, 0, dwVideoDataLen);
                memcpy(pFrame->m_pbH264FrameData, pbVideoData, dwVideoDataLen);
                pFrame->m_iDataSize = dwVideoDataLen;
            }
            pFrame->m_isIFrame = dwVideoType == VIDEO_TYPE_H264_HISTORY_I ? true : false;
            pFrame->m_llFrameTime = dw64TimeMS;
            pFrame->m_iWidth = dwVideoWidth;
            pFrame->m_iHeight = dwVideoHeight;
            pFrame->index = dwCarID;

            m_FrameMt.lock();
            m_FrameList.push_back(pFrame);
            m_FrameMt.unlock();
            //            DebugLog(QString("push to frame list,list size
            //            %1").arg(m_FrameList.size()));
        }
    } else {
        if (dwCarID == H264_FLAG_HISTROY_END) {
            DebugLog(QString("CardId:%1,视频结束帧").arg(dwCarID));
            QMutexLocker locker(&m_FrameMt);
            if (m_FrameList.isEmpty()) {
                return 0;
            }
            bool bHaveSaved = false;
            CustH264Struct *pFrame = m_FrameList.front();
            if (!pFrame) {
                DebugLog(QString("第一帧图像为空,清空视频"));
                ClearFrameList();
                return 0;
            }

            {
                QMutexLocker locker(&m_cardIdMutex);
                foreach (VideoCarIdInfo Id, m_CarIdList) {
                    if (pFrame->index == Id.dwCarId) {
                        bHaveSaved = true;
                        break;
                    }
                }
            }

            if (!bHaveSaved && !m_FrameList.isEmpty()) {
                QDateTime occurTime = QDateTime::fromMSecsSinceEpoch(pFrame->m_llFrameTime);
                QString sFileName = QString("%1_%2.mp4")
                                        .arg(pFrame->index)
                                        .arg(occurTime.toString("yyyyMMddhhmmss"));

                sFileName = m_sImagePath + sFileName;
                CustH264Struct *pLastFrame =
                    new CustH264Struct(pFrame->m_pbH264FrameData, pFrame->m_iDataSize,
                                       pFrame->m_iWidth, pFrame->m_iHeight, pFrame->m_isIFrame,
                                       pFrame->m_isHistory, pFrame->m_llFrameTime, pFrame->index);

                m_FrameList.push_back(pLastFrame);
                DebugLog(QString("save first frame, list size:%1, filename:%2")
                             .arg(m_FrameList.size())
                             .arg(sFileName));

                if (saveFrameToVideo(m_FrameList, sFileName.toLocal8Bit().data())) {
                    DebugLog(QString("save video file:%1").arg(sFileName));
                }
                VideoCarIdInfo carInfo;
                carInfo.dwCarId = pFrame->index;
                carInfo.sVideoFileName = sFileName;

                {
                    QMutexLocker locker(&m_cardIdMutex);
                    while (m_CarIdList.size() > 5) {
                        VideoCarIdInfo videoInfo = m_CarIdList.front();
                        m_CarIdList.pop_front();

                        if (videoInfo.sVideoFileName.length() > 0)
                            RemoveFile(videoInfo.sVideoFileName);
                    }
                    m_CarIdList.push_back(carInfo);
                }
            }
            DebugLog("清除视频帧数据");
            ClearFrameList();
            DebugLog(QString("视频处理完毕"));
        }
    }
    return 0;
}

int VCRDev::OnHvCallBackH264(DWORD dwCarID, DWORD dwVideoType, DWORD dwVideoWidth,
                             DWORD dwVideoHeight, DWORD64 dw64TimeMS, PBYTE pbVideoData,
                             DWORD dwVideoDataLen, LPCSTR szVideoExtInfo)
{
    QString sFrameType;
    switch (dwVideoType) {
        case VIDEO_TYPE_H264_HISTORY_I:
            sFrameType = QString("I_Frame");
            break;
        case VIDEO_TYPE_H264_HISTORY_P:
            sFrameType = QString("P_Frame");
            break;
        default:
            sFrameType = QString("UNKNOWN Frame");
            break;
    }

    if (dwVideoType == VIDEO_TYPE_H264_HISTORY_I || VIDEO_TYPE_H264_HISTORY_P == dwVideoType) {
        if (dwCarID != H264_FLAG_HISTROY_END) {
            CustH264Struct *pFrame = new CustH264Struct();
            pFrame->m_pbH264FrameData = new unsigned char[dwVideoDataLen];
            if (pFrame->m_pbH264FrameData) {
                memset(pFrame->m_pbH264FrameData, 0, dwVideoDataLen);
                memcpy(pFrame->m_pbH264FrameData, pbVideoData, dwVideoDataLen);
                pFrame->m_iDataSize = dwVideoDataLen;
            }
            pFrame->m_isIFrame = (dwVideoType == VIDEO_TYPE_H264_HISTORY_I);
            pFrame->m_llFrameTime = dw64TimeMS;
            pFrame->m_iWidth = dwVideoWidth;
            pFrame->m_iHeight = dwVideoHeight;
            pFrame->index = dwCarID;

            m_FrameMt.lock();
            m_FrameList.push_back(pFrame);
            m_FrameMt.unlock();
        }
    } else {
        if (dwCarID == H264_FLAG_HISTROY_END) {
            DebugLog(QString("CardId:%1,视频结束帧").arg(dwCarID));
            QMutexLocker locker(&m_FrameMt);
            if (m_FrameList.isEmpty()) {
                return 0;
            }

            bool bHaveSaved = false;
            CustH264Struct *pFrame = m_FrameList.front();
            if (!pFrame) {
                DebugLog(QString("第一帧图像为空,清空视频"));
                ClearFrameList();
                return 0;
            }

            // 判断是否已保存视频
            {
                QMutexLocker locker(&m_cardIdMutex);
                foreach (VideoCarIdInfo Id, m_CarIdList) {
                    if (pFrame->index == Id.dwCarId) {
                        bHaveSaved = true;
                        break;
                    }
                }
            }

            if (!bHaveSaved && !m_FrameList.isEmpty()) {
                QDateTime occurTime = QDateTime::fromMSecsSinceEpoch(pFrame->m_llFrameTime);
                QString sFileName = QString("%1_%2.mp4")
                                        .arg(pFrame->index)
                                        .arg(occurTime.toString("yyyyMMddhhmmss"));

                sFileName = m_sImagePath + sFileName;
                DebugLog(QString("save first frame, list size:%1, filename:%2")
                             .arg(m_FrameList.size())
                             .arg(sFileName));

                // 打开文件准备写入
                QFile h264File(sFileName);
                if (h264File.open(QIODevice::WriteOnly)) {
                    // 将帧数据写入文件
                    foreach (CustH264Struct *frame, m_FrameList) {
                        h264File.write(reinterpret_cast<const char *>(frame->m_pbH264FrameData),
                                       frame->m_iDataSize);
                    }
                    h264File.close();
                    DebugLog(QString("保存 H264 文件:%1").arg(sFileName));
                } else {
                    DebugLog(QString("无法打开文件:%1").arg(sFileName));
                }

                VideoCarIdInfo carInfo;
                carInfo.dwCarId = pFrame->index;
                carInfo.sVideoFileName = sFileName;

                {
                    QMutexLocker locker(&m_cardIdMutex);
                    while (m_CarIdList.size() > 5) {
                        VideoCarIdInfo videoInfo = m_CarIdList.front();
                        m_CarIdList.pop_front();
                    }
                    m_CarIdList.push_back(carInfo);
                }
            }
            DebugLog("清除视频帧数据");
            ClearFrameList();
            DebugLog(QString("视频处理完毕"));
        }
    }
    return 0;
}

void VCRDev::OnVcrResult(QString sGuid)
{
    if (!m_pRequestHandler) return;

    VcrResult vcrResult;
    bool bRlt = m_pRequestHandler->GetVcrResultByGuid(sGuid, vcrResult);
    if (!bRlt) {
        return;
    }
    if (IsValidVehPlate(vcrResult.sPlate)) {
        vcrResult.bPlateValid = 1;
    } else {
        vcrResult.bPlateValid = 0;
    }

    VcrResult *pResult = new VcrResult();
    *pResult = vcrResult;

    QString sPlate = vcrResult.sPlate;
    int nColor = vcrResult.nColor;
    int vc = vcrResult.vehclass;
    int axis = vcrResult.nAxleCnt;
    quint32 dwCarId = vcrResult.dwCarID;

    m_LockResult.lock();
    //将结果加入队列
    VcrResult *pLastResult = NULL;
    if (m_listHvResult.size() > 0) pLastResult = m_listHvResult.last();
    if (pLastResult) {
        if (pResult->dwCarID == pLastResult->dwCarID) {
            m_listHvResult.pop_back();
            SafeDelMemory(pLastResult, false);
        }
    }
    m_listHvResult.push_back(pResult);
    DebugLog(QString("车型识别结束,%1_%2,车型:%3,轴数:%4,carId:%5")
                 .arg(sPlate)
                 .arg(nColor)
                 .arg(vc)
                 .arg(axis)
                 .arg(dwCarId));
    // 结果由处理者释放
    m_pcurHvResult = NULL;
    // 判断数据的数量
    while (m_listHvResult.size() > MAX_RESULT_COUNT) {
        VcrResult *pFirstResult = m_listHvResult.front();
        m_listHvResult.pop_front();
        if (pFirstResult) {
            pFirstResult->RemoveImgFile();
            SafeDelMemory(pFirstResult, false);
        }
    }
    m_LockResult.unlock();
    //接收到数据, 发出通知
    emit OnVcrResultData(dwCarId, vcrResult.nColor, vcrResult.sPlate, vcrResult.vehclass,
                         vcrResult.nAxleCnt);
}

void VCRDev::OnHearBeat() {}

bool VCRDev::StartDev()
{
    if (1 == m_btype) {
        if (m_pHttpListener) {
            m_pHttpListener->listen();
            return m_pHttpListener->isListening();
        }
        QString sPath = GetCurrentPath() + QString("webapp.ini");
        QSettings *listenerSetting = new QSettings(sPath, QSettings::IniFormat);
        m_pRequestHandler = new VcrRequestHandler(this);
        m_pRequestHandler->SetImagePath(m_sImagePath);

        connect(m_pRequestHandler, SIGNAL(NotifyVcrResult(QString)), this,
                SLOT(OnVcrResult(QString)));
        connect(m_pRequestHandler, SIGNAL(NotifyHearBeat()), this, SLOT(OnHearBeat()));
        m_pHttpListener = new HttpListener(listenerSetting, m_pRequestHandler, this);
        return m_pHttpListener->isListening();
    }

    if (m_sConnStr1.isEmpty()) {
        ErrorLog("车型设备ip地址未配置");
        return false;
    }
    if (m_hDevice != NULL) {
        HVAPI_CloseEx(m_hDevice);
        m_hDevice = NULL;
    }
    m_hDevice = HVAPI_OpenEx(m_sConnStr1.toLatin1().data(), NULL);
    if (m_hDevice == NULL) {
        ErrorLog(QString("调用HVAPI_OpenEx，返回空: ip:%1").arg(m_sConnStr1.toLatin1().data()));
        return false;
    }

    // 设备回调
    if ((HVAPI_SetCallBackEx(m_hDevice, (PVOID)OnHvResultBeginProxy, this, 0,
                             CALLBACK_TYPE_RECORD_INFOBEGIN, NULL) != S_OK) ||
        (HVAPI_SetCallBackEx(m_hDevice, (PVOID)OnHvResultEndProxy, this, 0,
                             CALLBACK_TYPE_RECORD_INFOEND, NULL) != S_OK) ||
        (HVAPI_SetCallBackEx(m_hDevice, (PVOID)OnHvResultBigImageProxy, this, 0,
                             CALLBACK_TYPE_RECORD_BIGIMAGE, NULL) != S_OK) ||
        (HVAPI_SetCallBackEx(m_hDevice, (PVOID)OnHvResultSmallImageProxy, this, 0,
                             CALLBACK_TYPE_RECORD_SMALLIMAGE, NULL) != S_OK) ||
        (HVAPI_SetCallBackEx(m_hDevice, (PVOID)OnHvResultBinImageProxy, this, 0,
                             CALLBACK_TYPE_RECORD_BINARYIMAGE, NULL) != S_OK) ||
        (HVAPI_SetCallBackEx(m_hDevice, (PVOID)OnHvResultPlateProxy, this, 0,
                             CALLBACK_TYPE_RECORD_PLATE, NULL) != S_OK)) {
        ErrorLog("调用HVAPI_SetCallBackEx，设置回调函数失败!");
        return false;
    }

    if (m_bSendVideo) {
        if (HVAPI_SetCallBackEx(m_hDevice, (PVOID)OnHvCallBackH264Proxy, this, 0,
                                CALLBACK_TYPE_RECORD_ILLEGALVIDEO, NULL) != S_OK) {
            ErrorLog(QString("设置短视频回调函数失败"));
            // return false;
        } else {
            DebugLog(QString("设置短视频传出回调成功"));
        }
    } else {
        DebugLog(QString("车型识别不传输小视频"));
    }

    m_WorkThread.SetVcrDev(this);
    m_WorkThread.ResumeThread();

    return true;
}

void VCRDev::CloseDev()
{
    if (1 == m_bStd) {
        if (m_pHttpListener) m_pHttpListener->close();
        return;
    }

    m_WorkThread.StopThread();
    if (m_hDevice != NULL) {
        HVAPI_CloseEx(m_hDevice);
        m_hDevice = NULL;
    }
}

bool VCRDev::LoadDriver()
{
    m_sImagePath = GetCurrentPath() + "vcrimg/";
    CreatePath(m_sImagePath);

    if (1 == m_btype) {  // httpserver
        return true;
    }

    if (m_bDriverLoaded) {
        return true;
    }
    QString sPath = GetCurrentPath() + m_sDriver;
    m_hLibModule.setFileName(sPath);

    //
    if (!m_hLibModule.load()) {
        m_lastError = QString("加载自动车型%1失败").arg(sPath);
        ErrorLog(m_lastError);
        ChangeStatus(DEV_STATUS_LibErr);
        return false;
    }
    // 1. 打开设备句柄
    HVAPI_OpenEx = (Func_HVAPI_OpenEx)LoadFunc(m_hLibModule, "HVAPI_OpenEx");
    // 2. 关闭设备句柄
    HVAPI_CloseEx = (Func_HVAPI_CloseEx)LoadFunc(m_hLibModule, "HVAPI_CloseEx");
    // 3. 设置回调函数
    HVAPI_SetCallBackEx = (Func_HVAPI_SetCallBackEx)LoadFunc(m_hLibModule, "HVAPI_SetCallBackEx");
    // 4. 检测连接
    HVAPI_GetConnStatusEx =
        (Func_HVAPI_GetConnStatusEx)LoadFunc(m_hLibModule, "HVAPI_GetConnStatusEx");
    if (NULL == HVAPI_OpenEx || NULL == HVAPI_CloseEx || NULL == HVAPI_SetCallBackEx ||
        NULL == HVAPI_GetConnStatusEx) {
        ErrorLog(QString("获取车型识别动态库[%1]中一个或多个函数失败").arg(sPath));
        ReleaseDriver();
        ChangeStatus(DEV_STATUS_LibErr);
        return false;
    }

    m_bDriverLoaded = true;
    return true;
}

void VCRDev::ReleaseDriver()
{
    // 将动态库置为未加载
    // m_bDriverLoaded = false;
    // 1. 打开设备句柄
    HVAPI_OpenEx = NULL;
    // 2. 关闭设备句柄
    HVAPI_CloseEx = NULL;
    if (m_bDriverLoaded) {
        m_hLibModule.unload();
        m_bDriverLoaded = false;
    }
}

/*根据车牌号获取车型结果, 先严格匹配，如果颜色对不上，再模糊匹配。此车之前的数据全部清除*/
VcrResult *VCRDev::GetVcrResult(int nColor, const QString sPlate)
{
    DebugLog("GetVcrResult -- begin");
    //尽车牌匹配的索引值
    int tempTargetIdx = -1;
    //全匹配的索引值
    int targetIdx = -1;
    DebugLog(QString("取车辆%1_%2车型识别结果").arg(sPlate).arg(nColor));
    for (int i = 0; i < m_listHvResult.size(); i++) {
        VcrResult *pItem = m_listHvResult.at(i);
        if (!pItem) {
            continue;
        }
        if (pItem->bNoPlate()) {
            continue;
        }
        int nRlt = sPlate == pItem->sPlate ? 1 : 0;  // compareVlp(sPlate, pItem->sPlate);
        DebugLog(QString("比较车牌%1_%2,返回%3").arg(pItem->nColor).arg(pItem->sPlate).arg(nRlt));
        if (nRlt >= 1 && nRlt <= 3) {
            if (pItem->nColor == nColor) {
                //车牌号颜色都匹配
                if (pItem->vehclass > 0 && pItem->vehclass <= 26) {
                    DebugLog(QString("%1匹配到车型:%2").arg(sPlate).arg(pItem->vehclass));
                    targetIdx = i;
                    // break;//同一辆车会上送多次识别信息，最后一次包含所有图片信息
                } else {
                    DebugLog(QString("%1匹配到车型:%2,车型错误,继续查找")
                                 .arg(sPlate)
                                 .arg(pItem->vehclass));
                }
            } else if (tempTargetIdx == -1) {
                //只匹配到了车牌
                tempTargetIdx = i;
            }
        }
    }
    if (targetIdx == -1) {
        targetIdx = tempTargetIdx;
    }
    if (targetIdx == -1) {
        return NULL;
    } else {
        //先清除此车前面的记录 - 已屏蔽此功能
        /*
        while (targetIdx > 0) {
            VcrResult *pItem = m_listHvResult.first();
            m_listHvResult.removeFirst();
            if (pItem) {
                SavePreVcrResult(pItem);
            }
            // pItem->RemoveImgFile();
            // SafeDelMemory(pItem, false);
            targetIdx--;
        }
        */
        //取出记录
        return m_listHvResult.at(targetIdx);
    }
}

VcrResult *VCRDev::GetVcrResult_Fuzzy(int nColor, const QString &sPlate)
{
    DebugLog("GetVcrResult_Fuzzy -- begin");
    //尽车牌匹配的索引值
    int tempTargetIdx = -1;
    //全匹配的索引值
    int targetIdx = -1;
    DebugLog(QString("取车辆%1_%2车型模糊识别结果").arg(sPlate).arg(nColor));
    if (m_listHvResult.isEmpty()) {
        DebugLog(QString("车型识别对列为空,无匹配结果"));
        return NULL;
    }
    for (int i = 0; i < m_listHvResult.size(); i++) {
        VcrResult *pItem = m_listHvResult.at(i);
        if (!pItem || pItem->bNoPlate()) continue;
        int nRlt = compareVlp_funnzzy(sPlate, pItem->sPlate);
        DebugLog(
            QString("模糊比较车牌%1_%2,返回%3").arg(pItem->nColor).arg(pItem->sPlate).arg(nRlt));
        if (nRlt < 3) {  //少于3位则认为匹配成功
            //车牌号颜色都匹配
            if (nColor == pItem->nColor) {
                if (pItem->vehclass > 0 && pItem->vehclass <= 26) {
                    DebugLog(QString("%1模糊匹配到车型:%2,车牌:%3")
                                 .arg(sPlate)
                                 .arg(pItem->vehclass)
                                 .arg(pItem->sPlate));
                    targetIdx = i;
                    // break;//同一辆车会上送多次识别信息，最后一次包含所有图片信息
                } else {
                    DebugLog(QString("%1模糊匹配到车型:%2,%3车型错误,继续查找")
                                 .arg(sPlate)
                                 .arg(pItem->vehclass)
                                 .arg(pItem->sPlate));
                }
            } else {
                tempTargetIdx = i;
                DebugLog(QString("%1模糊匹配到车型:%2,%3颜色不符,继续查找")
                             .arg(sPlate)
                             .arg(pItem->vehclass)
                             .arg(pItem->sPlate));
            }
        }
    }
    if (targetIdx == -1) {
        targetIdx = tempTargetIdx;
    }
    if (targetIdx == -1) {
        return NULL;
    } else {
        //先清除此车前面的记录 - 已屏蔽此功能
        /*
        while (targetIdx > 0) {
            VcrResult *pItem = m_listHvResult.first();
            m_listHvResult.removeFirst();
            if (pItem) {
                SavePreVcrResult(pItem);
            }
            // pItem->RemoveImgFile();
            // SafeDelMemory(pItem, false);
            targetIdx--;
        }
        */
        //取出记录
        return m_listHvResult.at(targetIdx);
    }
}

QString VCRDev::GetVideoFileName(quint32 dwCarId)
{
    QMutexLocker locker(&m_cardIdMutex);
    DebugLog(QString("取%1视文件名").arg(dwCarId));
    if (m_CarIdList.isEmpty()) return QString("");

    foreach (VideoCarIdInfo carInfo, m_CarIdList) {
        if (carInfo.dwCarId == dwCarId) {
            DebugLog(QString("取%1视文件名:%2").arg(dwCarId).arg(carInfo.sVideoFileName));
            return carInfo.sVideoFileName;
        }
    }
    return QString("");
}

/*根据车牌号获取车型结果, 先严格匹配，如果颜色对不上，再模糊匹配。此车之前的数据全部清除
  取回的数据，由使用方清除*/
CVehClass VCRDev::GetVehClass(int nColor, const QString sPlate)
{
    DebugLog(QString("查询车牌%1_%2").arg(nColor).arg(sPlate));
    QMutexLocker locker(&m_LockResult);
    VcrResult *pResult = GetVcrResult(nColor, sPlate);
    if (pResult == NULL) {
        return VC_None;
    } else {
        return pResult->vehclass;
    }
}

bool VCRDev::GetVcrResult(int nColor, const QString &sPlate, VcrResult &vcrResult)
{
    DebugLog(QString("查询车牌%1_%2对应车型").arg(nColor).arg(sPlate));
    QMutexLocker locker(&m_LockResult);
    VcrResult *pResult = GetVcrResult(nColor, sPlate);
    if (pResult == NULL) {
        return false;
    } else {
        vcrResult = *pResult;
        return true;
    }
}

bool VCRDev::GetLastVcrResult(VcrResult &vcrResult)
{
    QMutexLocker locker(&m_LockResult);
    VcrResult *pResult = GetLastVcrResult();
    if (pResult) {
        vcrResult = *pResult;
        return true;
    } else
        return false;
}

bool VCRDev::GetFistVcrResult(VcrResult &vcrResult)
{
    QMutexLocker locker(&m_LockResult);
    if (m_listHvResult.isEmpty()) return false;
    VcrResult *pFirst = m_listHvResult.first();
    if (!pFirst) return false;

    quint32 dwCarId = pFirst->dwCarID;
    QList<VcrResult *>::iterator it = m_listHvResult.begin();
    for (; it != m_listHvResult.end(); ++it) {
        VcrResult *pResult = *it;
        if (pResult && pResult->dwCarID == dwCarId) {
            vcrResult = *pResult;
        }
    }
    return vcrResult.vehclass > 0;
}

void VCRDev::RemoveFirstCar(quint32 dwCarId)
{
    QMutexLocker locker(&m_LockResult);
    if (m_listHvResult.isEmpty()) return;

    VcrResult *pFirst = m_listHvResult.first();
    if (!pFirst) {
        m_listHvResult.pop_front();
        return;
    }

    quint32 curCarId = dwCarId;
    if (0 == curCarId) curCarId = pFirst->dwCarID;

    while (m_listHvResult.size() > 0) {
        VcrResult *pResult = m_listHvResult.first();
        if (pResult && pResult->dwCarID == curCarId) {
            DebugLog(QString("remove firstcar by id,carId:%1,%2")
                         .arg(pResult->dwCarID)
                         .arg(pResult->sPlate));
            m_listHvResult.pop_front();
            pResult->RemoveImgFile();
            SafeDelMemory(pResult, false);
            continue;
        } else {
            break;
        }
    }
    return;
}

void VCRDev::RemoveFirstCar(const QString &sVlp)
{
    QMutexLocker locker(&m_LockResult);
    if (m_listHvResult.isEmpty()) return;

    while (m_listHvResult.size() > 0) {
        VcrResult *pResult = m_listHvResult.first();
        if (pResult && pResult->sPlate == sVlp) {
            DebugLog(
                QString("removefirst car by vlp,cardId:%1,%2").arg(pResult->dwCarID).arg(sVlp));
            m_listHvResult.pop_front();
            pResult->RemoveImgFile();
            SafeDelMemory(pResult, false);
            continue;
        } else {
            break;
        }
    }
    return;
}

bool VCRDev::GetVcrResult_Fuzzy(int nColor, const QString &sPlate, VcrResult &vcrResult)
{
    QMutexLocker locker(&m_LockResult);
    VcrResult *pResult = GetVcrResult_Fuzzy(nColor, sPlate);
    if (pResult) {
        vcrResult = *pResult;
        return true;
    } else
        return false;
}

bool VCRDev::GetPreVcrResult(int nColor, const QString &sPlate, VcrResult &vcrResult)
{
    QMutexLocker locker(&m_LockPreResult);
    foreach (VcrResult *pItem, m_preResult) {
        if (pItem->nColor == nColor && pItem->sPlate == sPlate) {
            vcrResult = *pItem;
            return true;
        }
    }
    return false;
}

VcrResult *VCRDev::GetLastVcrResult()
{
    if (m_listHvResult.isEmpty())
        return NULL;
    else
        return m_listHvResult.last();
}

void VCRDev::SavePreVcrResult(VcrResult *pResult)
{
    m_LockPreResult.lock();
    m_preResult.push_back(pResult);
    while (m_preResult.size() > 2) {
        VcrResult *pResult = m_preResult.first();
        if (pResult) {
            pResult->RemoveImgFile();
            SafeDelMemory(pResult, false);
        }
        m_preResult.pop_front();
    }
    m_LockPreResult.unlock();
    return;
}

bool VCRDev::CheckConnectStatus()
{
    QDateTime dtCurrent = QDateTime::currentDateTime();
    qint64 tmSpan = dtCurrent.toMSecsSinceEpoch() - m_dtLastCheckConnect.toMSecsSinceEpoch();
    if (tmSpan > 20 * 1000 && m_hDevice != 0) {
        // 20秒检测一次
        if (HVAPI_GetConnStatusEx) {
            DWORD connectStatus;
            if (S_OK == HVAPI_GetConnStatusEx(m_hDevice, CONN_TYPE_RECORD, &connectStatus)) {
                // DebugLog(QString("vcr status:%1").arg(connectStatus));
                m_bConnectStatus = (connectStatus == CONN_STATUS_NORMAL);
            } else {
                ErrorLog("调用HVAPI_GetConnStatusEx返回失败");
                m_bConnectStatus = false;
            }
        }
        m_dtLastCheckConnect = dtCurrent;
    }
    if (m_bConnectStatus) {
        ChangeStatus(DEV_STATUS_OK);
    } else {
        ChangeStatus(DEV_STATUS_CommErr);
    }
    return m_bConnectStatus;
}

void VCRDev::ClearFrameList()
{
    foreach (CustH264Struct *pFrame, m_FrameList) {
        if (pFrame) {
            delete pFrame;
        }
    }

    m_FrameList.clear();
    /*
    while (m_FrameList.size() > 0) {
        CustH264Struct *pFrame = m_FrameList.front();
        m_FrameList.pop_front();
        if (pFrame) {
            delete pFrame;
        }
    }*/
}

void VCRDev::SetSendVideo(bool bSend) { m_bSendVideo = bSend; }

void VCRDev::AddVcrResult(VcrResult &vcrResult)
{
    VcrResult *pResult = new VcrResult;
    *pResult = vcrResult;
    m_listHvResult.push_back(pResult);
}

int VCRDev::GetVehCount()
{
    QMutexLocker locker(&m_LockResult);
    return m_listHvResult.size();
}

bool VCRDev::CheckVehPlateInQueueNotFirst(const QString &sVehPlate, QString &sError)
{
    if (sVehPlate.isEmpty()) {
        return false;
    }

    QMutexLocker locker(&m_LockResult);
    if (m_listHvResult.isEmpty()) {
        return false;
    }

    // 检查车牌是否在队列中
    bool bFoundInQueue = false;
    int nFoundIndex = -1;

    for (int i = 0; i < m_listHvResult.size(); ++i) {
        VcrResult *pResult = m_listHvResult.at(i);
        if (pResult && !pResult->sPlate.isEmpty() && pResult->sPlate == sVehPlate) {
            bFoundInQueue = true;
            nFoundIndex = i;
            break;
        }
    }

    // 如果在队列中找到了该车牌
    if (bFoundInQueue) {
        // 检查是否在队首（索引为0）
        if (nFoundIndex == 0) {
            // 在队首，允许交易
            DebugLog(QString("车牌%1在车型设备队列队首，允许交易").arg(sVehPlate));
            return false;
        } else {
            // 不在队首，拒绝交易
            sError = QString("车牌%1已在车型设备队列中但不在队首位置，请等待前车通过后再进行交易").arg(sVehPlate);
            DebugLog(QString("车牌%1在车型设备队列第%2位，拒绝交易").arg(sVehPlate).arg(nFoundIndex + 1));
            return true;
        }
    }

    // 不在队列中，允许交易
    return false;
}

bool CVcrThread::RunOnce()
{
    if (m_pVcrDev->GetDriverLoaded()) {
        //通知设备变化
        m_pVcrDev->CheckConnectStatus();
        msleep(600);
    } else {
        msleep(2000);
    }
    return true;
}
