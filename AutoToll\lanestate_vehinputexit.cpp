#include "lanestate_vehinputexit.h"

#include "cfarecalcunit.h"
#include "devicefactory.h"
#include "dlgmain.h"
#include "etclanectrl.h"
#include "forminputcardno.h"
#include "forminputenstationid.h"
#include "formselectstation.h"
#include "funcmenu.h"
#include "lanesoundplay.h"
#include "listdlg.h"
#include "soundplayer.h"
#include "speventmgr.h"
#include "stdlog.h"
#include "vehplatefunc.h"
#include "remotemsgmgr.h"
#include "formrepay.h"
#include "remotecontrolmgr.h"
#include "papercardmgr.h"
#include "vehtypelibmgr.h"
#include "etcprompt.h"

CLaneState_VehInputExit::CLaneState_VehInputExit(QObject *parent)
    : CLaneState_VehInput(CAbstractState::StateID_VehInputEx,parent)
{
    m_nCardPos = 0;
    
    // 初始化定时器并连接槽函数
    connect(&m_timerCardMgr, SIGNAL(timeout()), this, SLOT(OnCardMgrTimer()));
    // 连接定时器停止信号
    connect(Ptr_ETCCtrl, SIGNAL(NotifyStopCardMgrTimer()), this, SLOT(OnStopCardMgrTimer()));
}

CLaneState_VehInputExit::~CLaneState_VehInputExit() {
    // 停止定时器
    StopCardMgrTimer();
}

// 停止卡机定时器函数
void CLaneState_VehInputExit::StopCardMgrTimer(int type)
{
    if (m_timerCardMgr.isActive()) {
        if(type != 0)
        {
            if (Ptr_Info && Ptr_Info->GetCardMgrType() == 31){
                DebugLog(QString("停止定时器时伸出机械臂"));
                CDeviceFactory::GetPayMentMgr()->SetVehState(true);
            }
        }
        m_timerCardMgr.stop();
        DebugLog("停止出口车道卡机定时器");
    }
}

void CLaneState_VehInputExit::StartCardMgrTimer()
{
    int tempInterval = 0;

    if (!m_timerCardMgr.isActive()) {
        if(Ptr_Info)
        {
            tempInterval = m_nTimerCardMgrInterval2Flag ? m_nTimerCardMgrInterval2+Ptr_Info->GetCardTimerInterval() : Ptr_Info->GetCardTimerInterval();
        }else
        {
            tempInterval = m_nTimerCardMgrInterval2Flag ? m_nTimerCardMgrInterval2+timerCardMgrTime : timerCardMgrTime;
        }
        //CTransInfo temp;
        //Ptr_ETCCtrl->bAbnormalVehOfTheFirstVehInQueue(temp);
        //CTransInfo* temp = Ptr_ETCCtrl->GetLastTransInfo(DevIndex_Second);//取后天线的最新交易
        CTransInfo* temp = Ptr_ETCCtrl->RemoveTransInfoFromQue(true, false);
        if(temp)
            DebugLog(QString("transResult is %1, transState is %2").arg(temp->transResult).arg(temp->transState));
        else
            DebugLog(QString("未找到队首车辆"));

        if(!temp || (temp && !temp->bTransOk()))
        {
            m_timerCardMgr.start(tempInterval);
            DebugLog(QString("开启出口车道卡机定时器，间隔：%1ms，延时标志：%2")
                     .arg(tempInterval).arg(m_nTimerCardMgrInterval2Flag ? "已设置" : "未设置"));
        }else
        {
            DebugLog(QString("出口车道首车无未交易车辆，不开启定时器"));

        }
    }
}

void CLaneState_VehInputExit::MoveMgrHead()
{
    if (Ptr_Info && Ptr_Info->GetCardMgrType() == 31){
        DebugLog(QString("伸出机械臂"));
        CDeviceFactory::GetPayMentMgr()->SetVehState(true);
    }
}

void CLaneState_VehInputExit::Leave()
{
    CLaneState_VehInput::Leave();
    if (Ptr_Info->bGrantPaperCard()) {
        ControlScan(false);
    }
}

int CLaneState_VehInputExit::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    DebugLog(QString("处理按键:%1").arg(mtcKeyEvent->key()));
    int nRlt = CLaneState_VehInput::mtcKeyPressed(mtcKeyEvent);

    if (mtcKeyEvent->processed()) {
        return nRlt;
    }
    if (mtcKeyEvent->isFuncKey()) {
        int nFuncKey = mtcKeyEvent->func();
        mtcKeyEvent->setKeyType(KC_Func);
        switch (nFuncKey) {
        case KeyUp: {
#ifdef QT_DEBUG
            //            QString sCode = QString(
            //                        "36010205|5|36200050832362|1|0|b8d3413132333435|20250414055339|1|"
            //                        "1C6DF721F8A9FAAA3C1D0EA3BFE3B44C");

            //            OnQrCodeEvent(2, sCode);
            // CDeviceFactory::GetPayMentMgr()->EmitPressHelpButton(1);
            // ProcessCardMachineEvent(CCardMgr::CardMgrEvent_PressButton, 1, 1);
            //                QString sCode = QString(
            //                    "36014C2D|5|36240000000005|1|0|b8d3413232323232|20240426112450|1|"
            //                    "23BAF3AA5EF352FFC0E814CA06781D24");
            //                CScanQrCodeEvent *pEvent = new CScanQrCodeEvent(1, sCode);
            //                CAbstractDev::PostEvent(pEvent);
//            CJsonBuilder builder;
//            builder.AddKeyValue(QString("vlp"), QString::fromUtf8("赣A12345"));
//            builder.AddKeyValue_Int(QString("vlpc"), 1);
//            builder.AddKeyValue_Int(QString("type"), 2);
//            builder.AddKeyValue_Int(QString("remindLevel"), 2);
//            builder.AddKeyValue_Int(QString("cashTollCnt"), 10);
//            builder.AddKeyValue(QString("remark"), QString::fromUtf8("现金高频用户，重点推广，办理ETC每年通行费预计优惠56.12元"));

//            // 生成JSON字符串并赋值给promptMsg
//            QString promptMsg = QString("{%1}").arg(builder.CreateJsonStr().remove(0, 1));

//            // 使用通知处理器处理ETC提示，解耦业务逻辑
//            ETCNotificationHandler::GetInstance()->HandleETCPromptNotification(promptMsg);
            DebugLog("=== 开始测试车辆外轮廓数据获取功能 ===");
            
            // 获取称重设备实例
            CWtSysDev_ZC *pWeightDev = CDeviceFactory::GetWeightDev();
            if (!pWeightDev) {
                QString sMsg = QString("测试失败：获取称重设备实例失败");
                DebugLog(sMsg);
                ShowErrorMessage(sMsg);
                break;
            }
            
            // 创建测试用的车辆轴重信息对象
            CVehAxisInfo testVehAxisInfo;
            
            // 添加模拟的轴重数据（用于测试完整功能）
            testVehAxisInfo.AddRawAxis(1, 6500, 25);   // 第1轴：6.5吨，25km/h
            testVehAxisInfo.AddRawAxis(1, 8200, 25);   // 第2轴：8.2吨，25km/h
            testVehAxisInfo.AddRawAxis(2, 15300, 25);  // 第3-4轴：15.3吨，25km/h
            DebugLog("车辆外轮廓测试：添加模拟轴重数据 - 轴1:6.5T, 轴2:8.2T, 轴3-4:15.3T, 总重:30T");


            // 调用车辆外轮廓数据获取功能
            pWeightDev->GetVehicleOutlineInfo(testVehAxisInfo);
            
            
            // 显示测试结果
            QString sResultMsg;
            bool bHasOutlineData = (testVehAxisInfo.GetVehLength() > 0 || 
                                   testVehAxisInfo.GetVehWidth() > 0 || 
                                   testVehAxisInfo.GetVehHeight() > 0);
            bool bHasWeightData = (testVehAxisInfo.GetConfirmedTotalRawWeight() > 0);
            
            if (bHasOutlineData || bHasWeightData) {
                sResultMsg = QString("测试数据获取成功！\n外轮廓: %1×%2×%3mm\n总重: %4kg\n轴数: %5")
                            .arg(testVehAxisInfo.GetVehLength())
                            .arg(testVehAxisInfo.GetVehWidth())
                            .arg(testVehAxisInfo.GetVehHeight())
                            .arg(testVehAxisInfo.GetConfirmedTotalRawWeight())
                            .arg(testVehAxisInfo.GetConfirmedSingleAxisNum());
                
                DebugLog(QString("车辆综合测试成功 - 外轮廓[%1×%2×%3mm], 总重[%4kg], 轴数[%5]")
                        .arg(testVehAxisInfo.GetVehLength())
                        .arg(testVehAxisInfo.GetVehWidth())
                        .arg(testVehAxisInfo.GetVehHeight())
                        .arg(testVehAxisInfo.GetConfirmedTotalRawWeight())
                        .arg(testVehAxisInfo.GetConfirmedSingleAxisNum()));
            } else {
                sResultMsg = QString("测试数据获取失败\n请检查设备连接状态");
                DebugLog("车辆综合测试：未获取到有效数据");
            }
            
            // 显示测试结果到界面
            ShowErrorMessage(sResultMsg);
            
            // 如果获取到有效的测试数据，添加到称重系统中
            if (bHasOutlineData || bHasWeightData) {
                VehWeightInfo::GetVehWeightInfo()->AddVeh(testVehAxisInfo);
                DebugLog("车辆综合测试数据已添加到称重系统");
            } 
            // 即使没有获取到测试数据，也刷新界面显示
            //GetMainDlg()->NotifyWeightDataChange();
            
            DebugLog("=== 车辆外轮廓及称重数据综合测试完成 ===");

#endif
            break;
        }
        case KeyConfirm: {  //确认键
            //判断是否输入完成
            if (!IfInputVehInfoFinished()) {
                GetMainDlg()->ShowPromptMsg("请输入车辆信息", true);
            } else {
                //信息输入完成，判断是否已经读写卡完成
                CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
                if (!pCurTransInfo) {
                    return 0;
                }
                if (!pCurTransInfo->hasWriteCard_Exit()) {
                    //还未写卡
                    GetMainDlg()->ShowPromptMsg("请刷通行卡", true);
                } else {
                    //已经读写卡，根据重新输入的车辆信息，重新计费
                    ReProcessCalcFee();
                }
            }
            break;
        }
        case KeyBadCard:  //坏卡键
        {
            if (CAbstractState::m_bDataSaveFailed) {
                QString sError = QString("数据存储异常,停止交易,请联系维护人员!");
                DebugLog(sError);
                GetMainDlg()->ShowPromptMsg(sError, true);
                return false;
            }
            QString sErrorMsg;
            DebugLog(QString("坏卡键,开始坏卡处理..."));
            CCardReader::CancelCardDetection();
            if (!DoBadCard(sErrorMsg)) {
                CCardReader::StartCardDetection(this);
                GetMainDlg()->ShowPromptMsg(sErrorMsg, true);
                DebugLog(QString("坏卡处理失败"));
            } else {
                DebugLog(QString("坏卡处理成功"));
            }
            break;
        }
        case KeyNoCard: {
            QString sErrorMsg;
            CCardReader::CancelCardDetection();
            if (!DoNoCard(sErrorMsg)) {
                CCardReader::StartCardDetection(this);
                GetMainDlg()->ShowPromptMsg(sErrorMsg, true);
            }
            break;
        }

        case KeyPaperCard:  //纸卡键
        {
            QString sErrorMsg;
            CCardReader::CancelCardDetection();
            if (!DoPaperCard(sErrorMsg)) {
                GetMainDlg()->ShowPromptMsg(sErrorMsg, true);
                CCardReader::StartCardDetection(this);
            }
            break;
        }
        case KeyTicket: {  //票号菜单：换上或换下票据
            if (Ptr_Info->IsExitLane() && (!Ptr_Info->bEticket())) {
                QList<CListData> lstMenuItem;
                lstMenuItem.push_back(
                            CListData(0, "换上票据", "换上票据", NULL,
                                      Ptr_ETCCtrl->GetInvoiceInfo()->GetCurrentCount() <= 0, false));
                lstMenuItem.push_back(
                            CListData(1, "换下票据", "换下票据", NULL,
                                      Ptr_ETCCtrl->GetInvoiceInfo()->GetCurrentCount() > 0, false));
                lstMenuItem.push_back(
                            CListData(2, "修改票据", "修改票据", NULL,
                                      Ptr_ETCCtrl->GetInvoiceInfo()->GetCurrentCount() > 0, false));
                CListDlg dlg("发票操作", "请按【数字】键选择", false, true, GetMainDlg());
                dlg.ShowList(lstMenuItem, 0);
                int nResult = dlg.doModalShow();
                if (nResult == 1) {
                } else if (nResult == 2) {
                } else if (nResult == 3) {
                }
            }
            break;
        }
        case KeyReprint: {
            break;
        }
        case KeyBuyTicket: {
            FormRepay frmRepay;
            CVehInfo vehInfo;
            GetCurVehInfo(vehInfo);
            if (vehInfo.IsVLPEmpty()) {
                GetMainDlg()->ShowPromptMsg(QString("请输入补费车辆信息"));
                break;
            }
            int nRlt = CFuncMenu::DoRepayMenu();
            if (0 == nRlt) break;
            frmRepay.RepayMoney(vehInfo, nRlt);
            break;
        }
        case KeyRight: {
#ifdef QT_DEBUG
                QString sVlp = QString("WJD62511");
                int nColor = 3;
                CVehTypeLibMgr::GetVehTypeLibMgr()->AsyncGetVehResult(sVlp, nColor);
                //                GetMainDlg()->LoadVPRResult();
            RemoteMsgMgr::GetSingleInst()->SendCallVideoReq(QString("2"));

#endif
            break;
        }
        case KeyLeft: {
#ifdef QT_DEBUG
            //            QString sVlp = QString("WJD62511");
            //            int nColor = 3;
            //            CVehTypeLibMgr::GetVehTypeLibMgr()->AddQuryTask(sVlp, nColor);
            //                GetMainDlg()->LoadVPRResult();

            // ProcessCardMachineEvent(CCardMgr::CardMgrEvent_UpCardOk, 1, 1);
            //RemoteMsgMgr::GetSingleInst()->SendCallVideoReq(QString("1"));
            ETCPrompt::GetETCPrompt()->AddVehInfo(/*GB2312toUnicode*/(QString("赣AM93V9")),0);
#endif
            break;
        }
        case KeyDown: {
#ifdef QT_DEBUG
            //            CVehInfo_VehLib vehInfo;
            //            QString sVlp = QString("WJD62511");
            //            int nColor = 3;
            //            if (CVehTypeLibMgr::GetVehTypeLibMgr()->GetVehResult(sVlp, nColor, vehInfo)) {
            //                DebugLog(QString("ok,%1,%2").arg(vehInfo.vehClass).arg(vehInfo.score));
            //            }

            //              GetMainDlg()->LoadVCRResult();
            // ProcessCardMachineEvent(CCardMgr::CardMgrEvent_TakeCard, 1, 1);
            RemoteMsgMgr::GetSingleInst()->SendLaneReportEventReq(1, CSpEventMgr::SpEvent_Truck_OBU,
                                                                  QString("赣T00001\n货6\n等待确认"));
#endif
            break;
        }

        case KeyGreen: {
            //绿通键，显示历史绿通车二维码
            ShowHistoryQRCode();
            break;
        }
        }
    }
    return 0;
}

bool CLaneState_VehInputExit::ProcessDIEvent(qint32 nDI, bool bStatus, QString &sError)
{
    bool bRlt = CLaneState_VehInput::ProcessDIEvent(nDI, bStatus, sError);

    if (bRlt) {
        return true;
    }

    if (DI_LoopExist == nDI) {
        if (bStatus) {
            DebugLog("出口车辆压上存在线圈");
            if (Ptr_Info->bHaveCardMgr()) {
                /*
                VcrResult vcrResult;
                bool bMoved = true;
                bool bRlt = CDeviceFactory::GetVCRDev()->GetFistVcrResult(vcrResult);
                if (bRlt) {
                    if (vcrResult.vehclass > VC_Truck2 ||
                        (vcrResult.vehclass >= VC_Car3 && vcrResult.vehclass < VC_Truck)) {
                        DebugLog(
                            QString("车型识别首辆车车型%1,不移动卡机头").arg(vcrResult.vehclass));
                        bMoved = false;
                    }
                }

                if (bMoved) {
                    CDeviceFactory::GetPayMentMgr()->SetVehState(true);
                }*/
                // 当m_nCardMgrType=31时启动定时器
                if (Ptr_Info && Ptr_Info->GetCardMgrType() == 31) {
                    // 设置定时器间隔
                    //m_timerCardMgr.start(StartCardMgrTimer);
                    //StartCardMgrTimer();
                    //DebugLog("出口车道启动卡机定时器(m_nCardMgrType=31)");
                }else
                {
                    OnVehDetectTimer();
                }
                // TODO 判断是否进入验纸卡二维码时段。

                bool bPaperCard = false;
                if (Ptr_Info->bGrantPaperCard()) {
                    bPaperCard = CPaperCardMgr::GetSingleInst()->CheckHasStartPaper_Exit(sError);
                }
                if (bPaperCard) {
                    CDeviceFactory::GetAutoExTollScreen()->ShowHelp(
                                AutoExTollScreen::HT_InsertCard_PaperCard, "请插卡!");
                    // ControlScan(false);
                    // SleeperThread::msleep(200);
                    ControlScan(true);
                } else {
                    if (Ptr_Info->bTestPaper()) {
                        ControlScan(true);
                        CDeviceFactory::GetAutoExTollScreen()->ShowHelp(
                                    AutoExTollScreen::HT_InsertCard_PaperCard, "请插卡!");

                    } else
                        CDeviceFactory::GetAutoExTollScreen()->ShowHelp(
                                    AutoExTollScreen::HT_InsertCard, "请插卡!");
                }
                CDeviceFactory::FareDisplay_ShowHelpMsg(DevIndex_Second,
                                                        QString("请插入CPC卡\n或刷ETC卡"));
                CCardReader::StartCardDetection(this);
            }
            NotifyFrontRsuHasTrans();
            //}
        } else {
            DebugLog("出口车辆离开存在线圈");
            if (Ptr_Info->bHaveCardMgr()) {
                CDeviceFactory::GetAutoExTollScreen()->ShowHelp(
                            AutoExTollScreen::HT_WaitVehicleArrive, "等待车辆到来");
                CDeviceFactory::GetPayMentMgr()->SetVehState(false);
                ControlScan(false);
            }
        }
    }
    return true;
}

bool CLaneState_VehInputExit::ProcessCardMachineEvent(int nEvent, int nPos, int nIndex)
{
    QString sError;
    QString sFDMsg;
    bool bAlarm = false;
    CPaymentMachine *pCardMgr = CDeviceFactory::GetPayMentMgr();
    int nReaderIndex = CPaymentMachine::CardPos_Up == nPos ? 1 : 2;
    CCardReader *pCardReader = CDeviceFactory::GetCardReader(nReaderIndex);
    if (!pCardReader) {
        sError = QString("缴费机没配置卡读写器");
        DebugLog(QString("缴费机未配置读写器,请检查配置文件"));
        DisplayTransError_CPC(DevIndex_Second, sError, sFDMsg, bAlarm);
        return -1;
    }

    switch (nEvent) {
    case CBaseCardMgr::CardMgrEvent_CardInsert: {
        StopVehStayOutTimer();
        pCardReader->SetAutoReadCard(false);
        CCardReader::CancelCardDetection();
        DebugLog(QString("%1号卡机插卡,当天位置:%2").arg(nPos).arg(m_nCardPos));
        m_nCardPos = 2;
        SleeperThread::msleep(150);
        int nCardType = OpenCard(pCardReader, nPos, sError, sFDMsg);
        if (0 == nCardType) {
            if (Ptr_Info->RelocatedCard()) {
                m_nCardPos = 3;
                DebugLog(QString("%1,重新定位卡片到%2").arg(sError).arg(m_nCardPos));
                pCardMgr->SetCardPos(nPos, m_nCardPos);
                SetOpState(opState_None);
                return true;
            }
            pCardMgr->BackCard(nPos);
            DebugLog(QString("打开卡片失败,%1号卡机退卡").arg(nPos));
            DisplayTransError_CPC(DevIndex_Second, sError, sFDMsg, bAlarm);
            CDeviceFactory::GetAutoExTollScreen()->ShowHelp(AutoExTollScreen::HT_CardError,
                                                            "打开卡片失败，请重新插卡");

            SetOpState(opState_None);
            return true;
        }
        bool bReadFailed = false;
        bool bRlt = OnCardInsertEvent(pCardReader, nCardType, nPos, sError, sFDMsg, bAlarm,
                                      bReadFailed);
        if (!bRlt) {
            pCardReader->CloseCard();
            if (Ptr_Info->RelocatedCard() && bReadFailed) {
                m_nCardPos = 3;
                DebugLog(QString("%1,重新定位卡片到%2").arg(sError).arg(m_nCardPos));
                pCardMgr->SetCardPos(nPos, m_nCardPos);
                SetOpState(opState_None);
                return true;
            }
            if (bReadFailed) {
                CDeviceFactory::GetAutoExTollScreen()->ShowHelp(AutoExTollScreen::HT_CardError,
                                                                "读卡失败，请重新插卡");
            }
            SleeperThread::msleep(100);
            pCardMgr->BackCard(nPos);
            DisplayTransError_CPC(DevIndex_Second, sError, sFDMsg, bAlarm);
            DebugLog(QString("插卡事件处理失败,%1").arg(sError));
            SetOpState(opState_None);
        }
        break;
    }
    case CBaseCardMgr::CardMgrEvent_Relocal: {
        DebugLog(QString("%1号卡机,卡片定位到%2成功").arg(nPos).arg(m_nCardPos));
        SleeperThread::msleep(100);
        int nCardType = OpenCard(pCardReader, nPos, sError, sFDMsg);
        if (0 == nCardType) {
            if (m_nCardPos == 3) {
                m_nCardPos = 1;
                pCardMgr->SetCardPos(nPos, m_nCardPos);
                DebugLog(
                            QString("%1号卡机打开卡片失败,重新定位到%2").arg(nPos).arg(m_nCardPos));
            } else {
                // CCardReader::StartCardDetection(this);
                pCardMgr->BackCard(nPos);
                DebugLog(QString("%1号卡机重新定位卡片%2后打开卡片失败,退卡")
                         .arg(nPos)
                         .arg(m_nCardPos));
                DisplayTransError_CPC(DevIndex_Second, sError, sFDMsg, bAlarm);
                CDeviceFactory::GetAutoExTollScreen()->ShowHelp(AutoExTollScreen::HT_CardError,
                                                                "打开卡片失败，请重新插卡");
            }
            return true;
        }

        bool bReadFailed = false;
        bool bRlt = OnCardInsertEvent(pCardReader, nCardType, nPos, sError, sFDMsg, bAlarm,
                                      bReadFailed);
        if (!bRlt) {
            pCardReader->CloseCard();
            SleeperThread::msleep(100);
            if (bReadFailed) {
                if (3 == m_nCardPos) {
                    m_nCardPos = 1;
                    pCardMgr->SetCardPos(nPos, m_nCardPos);
                    DebugLog(
                                QString("%1号卡机读卡失败,重新定位到%2").arg(nPos).arg(m_nCardPos));
                    return true;
                }
                CDeviceFactory::GetAutoExTollScreen()->ShowHelp(AutoExTollScreen::HT_CardError,
                                                                "读卡失败，请重新插卡");
            }

            pCardMgr->BackCard(nPos);
            // CCardReader::StartCardDetection(this);
            DisplayTransError_CPC(DevIndex_Second, sError, sFDMsg, bAlarm);
            DebugLog(QString("重定位卡片事件处理失败,%1,退卡").arg(sError));
            SetOpState(opState_None);
        }
        break;
    }
    case CBaseCardMgr::CardMgrEvent_TakeCard: {
        DebugLog(QString("%1号卡机,卡片被取走").arg(nPos));
        CCardReader::StartCardDetection(this);
        SetOpState(opState_None);
        break;
    }
    default:
        break;
    }
    return true;
}

bool CLaneState_VehInputExit::OnOpenCardEvent(int nReadId, int nCardType, bool &bAlarm,
                                              QString &sError, bool &bContinueReadCard)
{
    QString sMsg, sFDMsg;
    StopVehStayOutTimer();
    if (CAbstractState::m_bDataSaveFailed) {
        sError = QString("数据存储异常,停止交易,请联系维护人员!");
        DebugLog(sError);
        bAlarm = true;
        return false;
    }

    bool bRlt = ProcessOpenCardEvent(nReadId, nCardType, bAlarm, sMsg, sFDMsg, bContinueReadCard);
    if (!bRlt) {
        DisplayTransError_CPC(DevIndex_Second, sMsg, sFDMsg, bAlarm);
        SetOpState(opState_None);
    }
    return bRlt;
}

bool CLaneState_VehInputExit::OnInputVehInfoByRemote(qint32 vehClass, int nPlateColor,
                                                     QString sVehPlate, qint32 vehType,
                                                     QString axleType)
{
    //出口，只输入车型和车牌
    if (vehClass != -1 && nPlateColor != -1 && axleType.isEmpty() && vehType == -1) {
        SetInputVehClass((CVehClass)vehClass);
        SetInputVehPlate(sVehPlate, nPlateColor);
        //信息输入完成，判断是否已经读写卡完成
        CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
        if (pCurTransInfo && pCurTransInfo->hasWriteCard_Exit()) {
            //已经读写卡，根据重新输入的车辆信息，重新计费
            ReProcessCalcFee();
        } else {
            //此时车辆信息输入完成，判断是否打开读写器
            if (Ptr_Info->IsExitLane()) CheckCardOperation();
            if (Ptr_Info->bHaveCardMgr()) {
                //发个语音提醒司机
                CDeviceFactory::GetAutoExTollScreen()->ShowHelp(AutoExTollScreen::HT_InsertCard,
                                                                "请插卡!");
                CDeviceFactory::FareDisplay_ShowHelpMsg(DevIndex_Second,
                                                        QString("请插入CPC卡\n或刷ETC卡"));
            }
            //默认车种
            RefreshVehTypeShow();
            //出口只设置了车型车牌，没有设置车种，触发一次刷新。
            Ptr_RemoteCtrl->RefreshVehInput();
        }
    }
    return true;
}

void CLaneState_VehInputExit::OnQrCodeEvent(int nPos, const QString &sResult)
{
    DebugLog(QString("%1号扫码:%2").arg(nPos).arg(sResult));
    if (Ptr_Info->bTestPaper()) {
        ShowLog(QString("扫码结果:%1").arg(sResult));
    }
    StopVehStayOutTimer();
    QString sError, sFDMsg;

    bool bAllowPaper = CPaperCardMgr::GetSingleInst()->CheckHasStartPaper_Exit(sError);
    bool bTestPaper = Ptr_Info->bTestPaper();
    if (!bAllowPaper && (!bTestPaper)) {
        DebugLog(sError);
        ShowErrorMessage(sError);
        return;
    }

    CVehEntryInfo vehEnInfo;
    QString sPaperNo;
    bool bAlarm = false;

    bool bRlt = ParsePaperQrCode(sResult, vehEnInfo, sPaperNo, sError);
    if (!bRlt) {
        // ShowErrorMessage(sError);
        DisplayTransError_CPC(DevIndex_Second, sError, sFDMsg, bAlarm);
        return;
    } else {
        QString sEnVlp = GB2312toUnicode(vehEnInfo.szEnVLP);
        QString sLog = QString("纸卡解析完毕,EnNet:%1,EnStation:%2,EnTime:%3,EnVLP:%4,EnVc:%5")
                .arg(vehEnInfo.sEnNetWorkIdHex)
                .arg(vehEnInfo.sEnStationHex)
                .arg(vehEnInfo.EnTime.toString("yyyyMMddhhmmss"))
                .arg(sEnVlp)
                .arg(vehEnInfo.bEnVC);
        DebugLog(sLog);

        if (!bAllowPaper) {
            ShowLog(sLog);
            return;
        }

        CTransInfo transInfo;
        if (Ptr_ETCCtrl->CheckVehhastransForPaperCard(sPaperNo, sEnVlp, transInfo, sError)) {
            bAlarm = true;
            DisplayTransError_CPC(DevIndex_Second, sError, sFDMsg, bAlarm);
            return;
        }
    }

    bRlt = ProcesspaperCardInfo(nPos, sPaperNo, vehEnInfo, sError, sFDMsg, bAlarm);
    if (!bRlt) {
        DisplayTransError_CPC(DevIndex_Second, sError, sFDMsg, bAlarm);
        SetOpState(opState_None);
        ControlScan(true);
    }
    return;
}

bool CLaneState_VehInputExit::ControlScan(bool bOpen)
{
    if (Ptr_Info->bHaveCardMgr()) {
        if (bOpen) {
            DebugLog(QString("打开扫码头"));
            /*
            connect(CDeviceFactory::GetPayMentMgr(), SIGNAL(NotifyScanResultEvent(QString)), this,
                    SLOT(OnScanResultEvent(QString)));*/

            CDeviceFactory::GetPayMentMgr()->OpenAllScan();
        } else {
            /*
            disconnect(CDeviceFactory::GetPayMentMgr(), SIGNAL(NotifyScanResultEvent(QString)),
                       this, SLOT(OnScanResultEvent(QString)));*/
            DebugLog(QString("关闭扫码头"));
            CDeviceFactory::GetPayMentMgr()->CloseAllScan();
        }
    }
    return true;
}

bool CLaneState_VehInputExit::ParsePaperQrCode(const QString &sQrCode, CVehEntryInfo &vehEnInfo,
                                               QString &sPaperNo, QString &sError)
{
    DebugLog(QString("纸卡处理扫描返回:%1").arg(sQrCode));

    QStringList sList = sQrCode.split(QString("|"));

    if (sList.size() < 3) {
        sError = QString("纸券二维码信息错误");
        DebugLog(QString("纸券二维码信息不完整"));
        return false;
    }

    vehEnInfo.Clear();
    // vehEnInfo.nEntryType = Entry_ByManual;
    vehEnInfo.sEnNetWorkIdHex = sList.at(0).left(4).toUpper();
    // vehEnInfo.wEnNetWorkId = vehEnInfo.sEnNetWorkIdHex.toInt();
    vehEnInfo.sEnStationHex = sList.at(0).right(4).toUpper();
    vehEnInfo.nEnLaneID = sList.at(1).toInt();
    vehEnInfo.sEnLaneHex = QString("%1").arg(vehEnInfo.nEnLaneID, 2, 16, QLatin1Char('0'));
    vehEnInfo.sExStationName = Ptr_Info->GetStationName();
    vehEnInfo.dwExStationID = Ptr_Info->GetStationID();

    sPaperNo = sList.at(2);
    vehEnInfo.bEnVC = 1;
    memset(vehEnInfo.szEnVLP, 0, sizeof vehEnInfo.szEnVLP);
    vehEnInfo.EnTime = QDateTime::currentDateTime().addSecs(-600);

    if (sList.size() >= 4) {
        vehEnInfo.bEnVC = sList.at(3).toInt();
    }
    if (sList.size() >= 6) {
        vehEnInfo.bEnVLPC = sList.at(4).toInt();
        QByteArray hexVlp = sList.at(5).toLocal8Bit();
        Hex2Raw((quint8 *)vehEnInfo.szEnVLP, hexVlp.data(), hexVlp.size());
    }

    if (sList.size() >= 7) {
        QString sEnTime = sList.at(6);
        if (sEnTime.length() != 14) {
            sError = "纸券入口格式错误";
            return false;
        }
        QDateTime enTime = QDateTime::fromString(sEnTime, "yyyyMMddhhmmss");
        if (!enTime.isValid()) {
            sError = QString("纸券入口时间无效");
            return false;
        }
        vehEnInfo.nEntryType = Entry_ByCard;
        vehEnInfo.EnTime = enTime;
    }

    QDateTime now = QDateTime::currentDateTime();

    int nPassTime = vehEnInfo.EnTime.secsTo(now);
    if (0 > nPassTime)
        vehEnInfo.nPassTime = 0;
    else
        vehEnInfo.nPassTime = nPassTime;

    vehEnInfo.bVehState = 0xff;

    if (sList.size() >= 9) {
        QString sMd5 = sList.at(8);
        QString sEnTime = vehEnInfo.EnTime.toString("yyyyMMddhhmmss");
        PaperCardNo_Param paperInfo;
        bool bGetKey = CPaperCardMgr::GetSingleInst()->GetEncryptionKey(sEnTime, paperInfo);
        if (bGetKey) {
            int nIndex = sQrCode.lastIndexOf(QString("|"));
            QString sSource = sQrCode.left(nIndex + 1);
            QByteArray bData = (sSource + paperInfo.encryption_key).toUtf8();
            QByteArray bMd5 =
                    QCryptographicHash::hash(bData, QCryptographicHash::Md5).toHex().toUpper();
            QString sCalcMd5 = QString::fromLocal8Bit(bMd5.data());
            DebugLog(QString("paper md5:%1").arg(sCalcMd5));
            if (sMd5 != sCalcMd5) {
                ErrorLog(QString("papermd5 failed,%1:%2").arg(sMd5).arg(sCalcMd5));
                sError = QString("纸卡校验失败");
                return false;
            }
        }
    } else {
        DebugLog(QString("纸卡扫码结果不完整"));
        sError = QString("纸卡信息不完整");
        // return false;
    }

    COrgBasicInfoTable *pBasicTable =
            (COrgBasicInfoTable *)CParamFileMgr::GetParamFile(cfOrgBasicInfo);
    int nProvinceId = vehEnInfo.sEnNetWorkIdHex.left(2).toInt();
    QString sStationHex = vehEnInfo.sEnNetWorkIdHex + vehEnInfo.sEnStationHex;
    COrgBasicInfo orgBasicInfo;
    bool bRlt =
            pBasicTable->QryOrgBasicInfo(nProvinceId, ORG_TYPE_STATION_GB, sStationHex, orgBasicInfo);
    if (!bRlt) {
        sError = QString("纸券入口站无效");
        return false;
    }

    vehEnInfo.sEnGBStationId = orgBasicInfo.sId;
    vehEnInfo.sEnStaionName = orgBasicInfo.sName;
    quint8 bStationId[2];
    memset(bStationId, 0, sizeof bStationId);
    Hex2Raw(bStationId, vehEnInfo.sEnStationHex);
    vehEnInfo.dwEnStationID = (quint32)qFromBigEndian<quint16>(bStationId);

    QString sLaneHex = sStationHex + vehEnInfo.sEnLaneHex;
    COrgBasicInfo orgLaneInfo;
    bRlt = pBasicTable->QryOrgBasicInfo(nProvinceId, ORG_TYPE_LANE_GB, sLaneHex, orgLaneInfo);
    if (!bRlt) {
        sError = QString("纸券入口车道错误");
        return false;
    } else {
        vehEnInfo.sEnGBLaneId = orgLaneInfo.sId;
        vehEnInfo.bEnLaneType = 1;
    }
    return true;
}

//电子纸卡专用
bool CLaneState_VehInputExit::ProcesspaperCardInfo(int nPos, const QString &sPaperNo,
                                                   CVehEntryInfo &vehEnInfo, QString &sError,
                                                   QString &sFDMsg, bool &bAlarm)
{
    bAlarm = false;
    if (!Ptr_ETCCtrl->bHasWaitingTransVeh(DevIndex_Manual)) {
        DebugLog("卡机检测到纸卡,但是没有等待处理车辆");
        sError = QString("未检测到等待交易车辆");
        sFDMsg = QString("未检测到车辆");
        bAlarm = true;
        return false;
    }

    CTransInfo *pRsuTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Second);
    if (!pRsuTransInfo) {
        DebugLog("卡机检测到纸卡卡,但是没有等待处理车辆");
        return false;
    }

    if (pRsuTransInfo->transState > CTransInfo::Ts_IsReadingIcc) {
        sError = QString("天线正在交易,纸卡扫码无效");
        return false;
    }

    CVehInfo vehInfo;
    CVehAxisInfo vehAxisInfo;
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
    if (!pCurTransInfo) {
        ErrorLog("onopenCardEvent,pCurTransInfo is error");
        sError = QString("未检测到车辆");
        return false;
    }

    if (Ptr_ETCCtrl->bAllowllContinuePass(false)) {
        // sError = QString("前车尚未离开");
        return false;
    }

    if (pCurTransInfo->hasWriteCard_Exit()) {
        sError = QString("当前已经刷卡,请按【确认】键计费");
        bAlarm = true;
        return false;
    }

    bool bRlt = GetVehInfoForCPC_New(false, pCurTransInfo, vehInfo, vehAxisInfo, sError, sFDMsg);
    if (!bRlt) {
        bAlarm = true;
        return false;
    }

    QString sVLp = GB2312toUnicode(pCurTransInfo->VehInfo.szVehPlate);
    int nPlateColor = pCurTransInfo->VehInfo.nVehPlateColor;
    int nErrorCode = 0;
    int nRlt = CheckVehList(DevIndex_Manual, sVLp, nPlateColor, nErrorCode, sError);
    if (nRlt > 0) {
        DisplayTransError_CPC(DevIndex_Second, sError, QString("异常车辆"), true);
        if (!ShowInformation_Help(QString("黑名单车辆"), sError,
                                  QString("<确定>键继续,<取消>键返回"), true, QString("黑名单车辆"),
                                  CSpEventMgr::SpEvent_BlackCar, true)) {
            return false;
        }
        if (3 == nRlt) {
            nRlt = CFuncMenu::ShowMenu_PrePayBList();
            if (0 == nRlt) return false;
            pCurTransInfo->m_nPrepayList = nRlt;
        }
    }

    QDateTime transTime = QDateTime::currentDateTime();

    //纸卡入口类型都按照人工选择处理
    if (vehEnInfo.nEntryType == Entry_None) vehEnInfo.nEntryType = Entry_ByManual;

    vehEnInfo.dwTotalWeight = pCurTransInfo->m_dwToTalWeight;
    vehEnInfo.dwWeightLimit = pCurTransInfo->m_dwWeightLimit;
    if (0 == vehEnInfo.dwTotalWeight) {
        vehEnInfo.dwTotalWeight = 1500;
    }
    vehEnInfo.VehicalAxles = pCurTransInfo->GetVehAxisNum(true);
    if (0 == vehEnInfo.VehicalAxles)
        vehEnInfo.VehicalAxles = GetVehAxisNumByVC(pCurTransInfo->VehInfo.VehClass);

    pCurTransInfo->SetVehEntryInfo(vehEnInfo, transTime);

    //填充纸卡卡号
    pCurTransInfo->mediaType = MediaType_Paper;
    pCurTransInfo->m_sPaperId = sPaperNo;

    int nDiscountType = 0;
    if (!CalculateTollFee(sError, sFDMsg, bAlarm, FeeClass_Min, nDiscountType, transTime)) {
        GetMainDlg()->ShowPromptMsg(sError, true);
        pCurTransInfo->ClearTransInfo();
        bAlarm = true;
        return false;
    }

    DebugLog(QString("纸卡计费成功,转入支付"));
    pCurTransInfo->m_bCardMgrIndex = nPos;
    Ptr_ETCCtrl->ChangeToVehMoneyState();
    return true;
}

void CLaneState_VehInputExit::fillEntryInfoBySelStation(CTransInfo *pCurTransInfo,
                                                        const COrgBasicInfo &stationInfo,
                                                        const COrgBasicInfo &laneInfo,
                                                        QDateTime transTime)
{
    CVehEntryInfo vehEntryInfo;
    vehEntryInfo.nEntryType = Entry_ByManual;
    vehEntryInfo.sEnNetWorkIdHex = stationInfo.sHex.left(4);
    // vehEntryInfo.wEnNetWorkId = vehEntryInfo.sEnNetWorkIdHex.toShort();
    vehEntryInfo.sEnStationHex = stationInfo.sHex.right(4);
    vehEntryInfo.dwEnStationID = vehEntryInfo.sEnStationHex.toUInt(0, 16);
    if (vehEntryInfo.sEnNetWorkIdHex == QString("3601") && vehEntryInfo.dwEnStationID < 3600000)
        vehEntryInfo.dwEnStationID = 3600000 + vehEntryInfo.dwEnStationID;
    vehEntryInfo.sEnStaionName = stationInfo.sName;
    vehEntryInfo.sEnGBStationId = stationInfo.sId;

    vehEntryInfo.dwExStationID = Ptr_Info->GetStationID();
    vehEntryInfo.sExStationName = Ptr_Info->GetStationName();
    vehEntryInfo.nEnLaneID = 0;
    vehEntryInfo.bEnLaneType = 1;
    vehEntryInfo.sEnLaneHex = QString("00");

    // QString sEnLaneHex = stationInfo.sHex + vehEntryInfo.sEnLaneHex;

    COrgBasicInfo orgLaneInfo;
    COrgBasicInfoTable *pTable = (COrgBasicInfoTable *)CParamFileMgr::GetParamFile(cfOrgBasicInfo);

    int nProvId = vehEntryInfo.sEnNetWorkIdHex.left(2).toInt();
    bool bRlt = pTable->GetFirstLane(nProvId, stationInfo.sHex, orgLaneInfo);

    // bool bRlt = pTable->QryOrgBasicInfo(nProvId, ORG_TYPE_LANE_GB, sEnLaneHex, orgLaneInfo);
    if (bRlt) {
        vehEntryInfo.sEnGBLaneId = orgLaneInfo.sId;
        vehEntryInfo.sEnLaneHex = orgLaneInfo.sHex.right(2);
        bool bOk = false;
        vehEntryInfo.nEnLaneID = vehEntryInfo.sEnLaneHex.toInt(&bOk, 16);
        DebugLog(QString("选择入口取%1第一条车道,%2,%3")
                 .arg(stationInfo.sHex)
                 .arg(orgLaneInfo.sHex)
                 .arg(vehEntryInfo.nEnLaneID));

    } else {
        DebugLog(QString("取%1第一条车道失败").arg(stationInfo.sHex));
        vehEntryInfo.sEnGBLaneId = vehEntryInfo.sEnGBStationId + QString("0000000");
        vehEntryInfo.sEnLaneHex = QString("00");
        vehEntryInfo.nEnLaneID = 0;
    }

    //手工选择入口站，入口时间比出口少2分钟即可
    vehEntryInfo.EnTime = transTime.addSecs(-300);
    vehEntryInfo.dwEnOper = 0;
    vehEntryInfo.bEnShiftId = 0;
    vehEntryInfo.bEnVC = pCurTransInfo->VehInfo.VehClass;
    vehEntryInfo.bEnVT = UVT_Normal;
    vehEntryInfo.bEnVLPC = pCurTransInfo->VehInfo.nVehPlateColor;
    memcpy(vehEntryInfo.szEnVLP, pCurTransInfo->VehInfo.szVehPlate, 12);
    vehEntryInfo.nPassTime = 0;
    vehEntryInfo.dwTotalWeight = pCurTransInfo->m_dwToTalWeight;
    vehEntryInfo.dwWeightLimit = pCurTransInfo->m_dwWeightLimit;
    if (0 == vehEntryInfo.dwTotalWeight) {
        vehEntryInfo.dwTotalWeight = 1500;
    }
    vehEntryInfo.VehicalAxles = pCurTransInfo->GetVehAxisNum(true);
    if (0 == vehEntryInfo.VehicalAxles)
        vehEntryInfo.VehicalAxles = GetVehAxisNumByVC(pCurTransInfo->VehInfo.VehClass);
    vehEntryInfo.gantryPassTime = 0;
    vehEntryInfo.sGantryNumHex.clear();
    vehEntryInfo.bVehState = 0xff;

    pCurTransInfo->SetVehEntryInfo(vehEntryInfo, transTime);
}

int CLaneState_VehInputExit::OpenCard(CCardReader *pCardReader, int nPos, QString &sError,
                                      QString sFDMsg)
{
    int nOpenTimes = 0;
    bool bRlt = false;
    while (!bRlt) {
        bRlt = pCardReader->OpenCard();
        ++nOpenTimes;
        if (bRlt || 3 == nOpenTimes) break;
        if (!bRlt) SleeperThread::msleep(80);
    }

    if (!bRlt) {
        sError = QString("%1号卡机打开卡片失败").arg(nPos);
        sFDMsg = QString("读卡失败\n请取卡后重新插卡");
        // CDeviceFactory::GetAutoExTollScreen()->ShowHelp(AutoExTollScreen::HT_CardError,
        // "读卡失败，请重新插卡");
        return 0;
    }

    int nCardType = pCardReader->GetCardType();
    return nCardType;
}

bool CLaneState_VehInputExit::fillEntryInfoByQry(CTransInfo *pCurTransInfo,
                                                 const CEntryQryResult &qryEntryInfo,
                                                 QDateTime transTime, QString &sError)
{
    CVehEntryInfo vehEntryInfo;
    vehEntryInfo.nEntryType = Entry_ByQry;

    // vehEntryInfo.wEnNetWorkId = qryEntryInfo.sNetIdHex.toShort();
    vehEntryInfo.dwEnStationID = qryEntryInfo.sEnStationHex.right(4).toUInt(0, 16);
    if (qryEntryInfo.sNetIdHex == QString("3601") && vehEntryInfo.dwEnStationID < 3600000)
        vehEntryInfo.dwEnStationID = 3600000 + vehEntryInfo.dwEnStationID;

    vehEntryInfo.dwExStationID = Ptr_Info->GetStationID();
    vehEntryInfo.sEnStaionName = qryEntryInfo.sEnStationName;
    vehEntryInfo.sExStationName = Ptr_Info->GetStationName();
    vehEntryInfo.nEnLaneID = qryEntryInfo.sEnLaneHex.right(2).toInt(0, 16);
    vehEntryInfo.EnTime =
            QDateTime::fromString(qryEntryInfo.enTime, "yyyy-MM-ddThh:mm:ss");  //入口时间
    if (!vehEntryInfo.EnTime.isValid()) vehEntryInfo.EnTime = transTime;

    vehEntryInfo.bEnLaneType = 1;
    vehEntryInfo.dwEnOper = 0;
    vehEntryInfo.bEnShiftId = 0;
    vehEntryInfo.bEnVC = pCurTransInfo->VehInfo.VehClass;
    vehEntryInfo.bEnVT = UVT_Normal;
    vehEntryInfo.bEnVLPC = pCurTransInfo->VehInfo.nVehPlateColor;
    memcpy(vehEntryInfo.szEnVLP, pCurTransInfo->VehInfo.szVehPlate, 12);
    vehEntryInfo.nPassTime = vehEntryInfo.EnTime.secsTo(transTime);
    vehEntryInfo.dwTotalWeight = pCurTransInfo->m_dwToTalWeight;
    vehEntryInfo.dwWeightLimit = pCurTransInfo->m_dwWeightLimit;
    if (0 == vehEntryInfo.dwTotalWeight) {
        vehEntryInfo.dwTotalWeight = 1500;
    }
    vehEntryInfo.VehicalAxles = pCurTransInfo->GetVehAxisNum(true);
    if (0 == vehEntryInfo.VehicalAxles)
        vehEntryInfo.VehicalAxles = GetVehAxisNumByVC(pCurTransInfo->VehInfo.VehClass);
    vehEntryInfo.gantryPassTime = 0;
    vehEntryInfo.sGantryNumHex.clear();
    vehEntryInfo.bVehState = 0xff;

    vehEntryInfo.sEnNetWorkIdHex = qryEntryInfo.sNetIdHex;
    vehEntryInfo.sEnStationHex = qryEntryInfo.sEnStationHex.right(4);
    vehEntryInfo.sEnLaneHex = qryEntryInfo.sEnLaneHex.right(2);

    vehEntryInfo.sEnGBStationId = qryEntryInfo.enStationId;  //入口站
    vehEntryInfo.sEnGBLaneId = qryEntryInfo.enTollLaneId;    //入口车道

    pCurTransInfo->SetVehEntryInfo(vehEntryInfo, transTime);
    return true;
}

bool CLaneState_VehInputExit::OnCardInsertEvent(CCardReader *pCardReader, int nCardType, int nPos,
                                                QString &sError, QString &sFDMsg, bool &bAlarm,
                                                bool &bReloadCard)
{
    sFDMsg.clear();
    sError.clear();
    QString sStateName;
    bReloadCard = false;

    if (!Ptr_ETCCtrl->bHasWaitingTransVeh(DevIndex_Manual)) {
        sError = QString("车道队列内无等待交易车辆");
        bAlarm = true;
        return false;
    }

    if (CAbstractState::m_bDataSaveFailed) {
        sError = QString("数据存储异常,停止交易,请联系维护人员!");
        DebugLog(sError);
        bAlarm = true;
        return false;
    }

    if (m_IsInputingVLP) {
        sError = QString("正在修改车辆信息,请稍后");
        sFDMsg = QString("正在输入车辆信息\n请稍后");
        return false;
    }

    CPaymentMachine *pCardMgr = CDeviceFactory::GetPayMentMgr();
    if (!pCardMgr) {
        sError = QString("未配置缴费机");
        return false;
    }

    if (!CheckAndSetOpState(opState_IsWritingCard, sStateName)) {
        sError = QString("%1,刷卡无效").arg(sStateName);
        return false;
    }

    if (!pCardReader->GetAuthResult()) {
        sError = QString("读写器未授权");
        return false;
    }

    if (Ptr_ETCCtrl->bAllowllContinuePass(false)) {
        // sError = QString("前车尚未离开");
        return false;
    }

    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
    if (!pCurTransInfo) {
        sError = QString("车道队列内无等待交易车辆");
        return false;
    }
    if (pCurTransInfo->hasWriteCard_Exit()) {
        QString sCardId;
        if (pCurTransInfo->mediaType == MediaType_OBU)
            sCardId = QString::fromAscii(pCurTransInfo->IccInfo.ProCardBasicInfo.szCardNo);
        else if (pCurTransInfo->mediaType == MediaType_CPC)
            sCardId = QString::fromAscii(pCurTransInfo->cpcIccInfo.cpcBasicInfo.sCardID);
        sError = QString("已刷卡,卡号[%1]").arg(sCardId);

        return false;
    }

    //如果有卡机，提示正在处理
    if (Ptr_Info->bHaveCardMgr()) {
        CDeviceFactory::GetAutoExTollScreen()->ShowHelp(AutoExTollScreen::HT_Checking,
                                                        "正在处理，请耐心等待");
    }

    if (TYPE_CPC != nCardType && TYPE_PRO != nCardType) {
        DebugLog(QString("卡类型%1,非CPC卡或ETC卡").arg(nCardType));
        sError = QString("非通行卡");
        sFDMsg = QString("非通行卡\n请插入通行卡");
        return false;
    }

    CVehInfo vehInfo;
    CVehAxisInfo vehAxisInfo;
    bool bRlt = GetVehInfoForCPC_New(false, pCurTransInfo, vehInfo, vehAxisInfo, sError, sFDMsg);
    if (!bRlt) {
        CDeviceFactory::GetAutoExTollScreen()->ShowHelp(AutoExTollScreen::HT_CheckError,
                                                        "信息核对失败，请转人工处理");
        bAlarm = true;
        return false;
    }

    RemoteMsgMgr::GetSingleInst()->SendCPCTransMsg_Exit(pCurTransInfo, 2);

    QString sVLp = GB2312toUnicode(pCurTransInfo->VehInfo.szVehPlate);
    int nPlateColor = pCurTransInfo->VehInfo.nVehPlateColor;

    CTransInfo transInfo;
    bool bHaveTrans = Ptr_ETCCtrl->CheckVehhasTransByVLP(sVLp, transInfo);
    if (bHaveTrans) {
        if (transInfo.bTransOk()) {
            sError = QString("%1本车道已交易,%2分钟之内不允许进行二次交易")
                    .arg(sVLp)
                    .arg(Ptr_Info->GetMaxInterval() / 60);
            DebugLog(sError);
            return false;
        }
    }

    int nErrorCode = 0;
    int nRlt = CheckVehList(DevIndex_Manual, sVLp, nPlateColor, nErrorCode, sError);
    if (nRlt > 0) {
        if (!ShowInformation_Help(QString("黑名单车辆"), sError,
                                  QString("<确定>键继续,<取消>键返回"), true, sFDMsg, nErrorCode,
                                  true)) {
            bAlarm = false;
            sFDMsg = QString("黑名单车辆");
            CDeviceFactory::GetAutoExTollScreen()->ShowHelp(AutoExTollScreen::HT_CheckError,
                                                            "信息核对失败，请转人工处理");
            return false;
        }
        if (3 == nRlt) {
            nRlt = CFuncMenu::ShowMenu_PrePayBList();
            if (0 == nRlt) return false;
            pCurTransInfo->m_nPrepayList = nRlt;
        }
    }

    bool bContinueReadCard = false;
    if (TYPE_PRO == nCardType) {
        sError = QString("请在ETC刷卡区刷卡");
        sFDMsg = sError;
        bRlt = false;
    } else if (TYPE_CPC == nCardType) {
        pCurTransInfo->m_bCardMgrIndex = nPos;
        bRlt = ProcessCPCCardEvent(pCardReader->GetCardId(), pCardReader, bContinueReadCard, sError,
                                   sFDMsg, bAlarm, bReloadCard);
        if (!bRlt) {
            pCurTransInfo->m_bCardMgrIndex = 0;
        }
    }
    return bRlt;
}

bool CLaneState_VehInputExit::ProcessOpenCardEvent(int nReadId, int nCardType, bool &bAlarm,
                                                   QString &sError, QString &sFDMsg,
                                                   bool &bContinueReadCard)
{
    bContinueReadCard = true;

    if (nCardType == TYPE_CPC) {
        if (nReadId == 1 || nReadId == 2) {
            DebugLog(QString("卡机读写器%1外置天线检测到cpc卡,不处理").arg(nReadId));
            return true;
        }
    }

    if (!Ptr_ETCCtrl->bHasWaitingTransVeh(DevIndex_Manual)) {
        DebugLog("卡机检测到刷ETC卡,但是没有等待处理车辆");
        sError = QString("未检测到等待交易车辆");
        sFDMsg = QString("未检测到车辆");
        return false;
    }

    CTransInfo *pRsuTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Second);
    if (!pRsuTransInfo) {
        DebugLog("卡机检测到刷ETC卡,但是没有等待处理车辆");
        return true;
    }

    if (pRsuTransInfo->transState > CTransInfo::Ts_IsReadingIcc) {
        sError = QString("天线正在交易,刷卡无效");
        return false;
    }

    CVehInfo vehInfo;
    CVehAxisInfo vehAxisInfo;
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
    if (!pCurTransInfo) {
        ErrorLog("onopenCardEvent,pCurTransInfo is error");
        sError = QString("未检测到车辆");
        return false;
    }

    if (Ptr_ETCCtrl->bAllowllContinuePass(false)) {
        // sError = QString("前车尚未离开");
        return false;
    }

    if (pCurTransInfo->hasWriteCard_Exit()) {
        sError = QString("已经刷卡,请按【确认】键计费");
        return false;
    }

    if (0 == nReadId) {  //外置读写器，需要判断是否有车辆信息，不自动取车型队列
        CVehInfo vehInfo;
        GetCurVehInfo(vehInfo, true);
        if (vehInfo.IsEmpty() || vehInfo.IsVLPEmpty()) {
            sError = QString("请输入车辆信息");
            return false;
        }
    }

    // bool bCheckInput = (1 == nReadId || 2 == nReadId);

    bool bRlt = GetVehInfoForCPC_New(true, pCurTransInfo, vehInfo, vehAxisInfo, sError, sFDMsg);
    if (!bRlt) {
        // if (vehInfo.IsEmpty()) bContinueReadCard = false;
        bAlarm = true;
        return false;
    }

    QString sVLp = GB2312toUnicode(pCurTransInfo->VehInfo.szVehPlate);
    int nPlateColor = pCurTransInfo->VehInfo.nVehPlateColor;
    CTransInfo transInfo;
    bool bHaveTrans = Ptr_ETCCtrl->CheckVehhasTransByVLP(sVLp, transInfo);
    if (bHaveTrans) {
        if (transInfo.bTransOk()) {
            sError = QString("%1本车道已交易,%2分钟之内不允许进行二次交易")
                    .arg(sVLp)
                    .arg(Ptr_Info->GetMaxInterval() / 60);
            DebugLog(sError);
            return false;
        }
    }
    //取车辆信息
    if (TYPE_PRO == nCardType) {
        RemoteMsgMgr::GetSingleInst()->SendETCTransMsg_Exit(pCurTransInfo, 2);
    } else {
        RemoteMsgMgr::GetSingleInst()->SendCPCTransMsg_Exit(pCurTransInfo, 2);
    }
    int nErrorCode = 0;
    int nRlt = CheckVehList(DevIndex_Manual, sVLp, nPlateColor, nErrorCode, sError);
    if (nRlt > 0) {
        DisplayTransError_CPC(DevIndex_Second, sError, QString("异常车辆"), true);
        if (!ShowInformation_Help(QString("黑名单车辆"), sError,
                                  QString("<确定>键继续,<取消>键返回"), true, QString("黑名单车辆"),
                                  CSpEventMgr::SpEvent_BlackCar, true)) {
            return false;
        }
        if (3 == nRlt) {
            nRlt = CFuncMenu::ShowMenu_PrePayBList();
            if (0 == nRlt) return false;
            pCurTransInfo->m_nPrepayList = nRlt;
        }
    }

    CCardReader *pReader = CCardReader::GetCardReader(nReadId);
    if (!pReader) {
        sError = QString("读写器未配置请联系维护人员");
        return false;
    }

    bRlt = false;
    pCurTransInfo->m_bCardMgrIndex = nReadId;
    if (TYPE_PRO == nCardType) {
        bContinueReadCard = true;
        bRlt = ProcessProCardEvent(nReadId, pReader->GetCardId(), pReader, bContinueReadCard,
                                   sError, sFDMsg, bAlarm);
        pReader->CloseCard();
    } else if (TYPE_CPC == nCardType) {
        bContinueReadCard = false;
        if (nReadId == 1 || nReadId == 2) {
            bContinueReadCard = true;
            return true;
        }
        bool bReloadCard = false;
        bRlt = ProcessCPCCardEvent(pReader->GetCardId(), pReader, bContinueReadCard, sError, sFDMsg,
                                   bAlarm, bReloadCard);
    }
    if (!bRlt) pCurTransInfo->m_bCardMgrIndex = 0;

    return bRlt;
}

bool CLaneState_VehInputExit::ProcessCPCCardEvent(quint32 dwCardMac, CCardReader *pCardReader,
                                                  bool &bContinueOpenCard, QString &sError,
                                                  QString &sFDMsg, bool &bAlarm, bool &bReLoadCard)
{
    bAlarm = false;
    bReLoadCard = false;
    CCPCBasicInfoRaw cpcBasicInfoRaw;
    memset(&cpcBasicInfoRaw, 0, sizeof cpcBasicInfoRaw);

    CCPCBatteryRaw cpcBatteryRaw;
    memset(&cpcBatteryRaw, 0, sizeof cpcBatteryRaw);
    CCPCTollInfoRaw cpcTollInfoRaw;
    CCPCRoadInfoRaw_New cpcRoadInfoRaw;
    CCPCTollCellInfoRaw_New cpcTollCellInfoRaw;

    CCPCIccInfo cpcIccInfo;
    cpcIccInfo.cpcCardMac = dwCardMac;
    bContinueOpenCard = true;

    bool bReadRlt = false;
    for (int i = 0; i < 2; ++i) {
        bReadRlt = pCardReader->ReadCPCCard_New(
                    &cpcIccInfo.cpcBasicInfo, &cpcIccInfo.cardTollInfo, &cpcIccInfo.cpcRoadInfo,
                    &cpcIccInfo.cpcTollCellInfo, cpcBasicInfoRaw, cpcBatteryRaw, cpcTollInfoRaw,
                    cpcRoadInfoRaw, cpcTollCellInfoRaw);
        if (!bReadRlt) {
            DebugLog(QString("第%1次读CPC卡失败").arg(i + 1));
            SleeperThread::msleep(100);
            continue;
        } else {
            DebugLog(QString("第%1次读CPC卡成功").arg(i + 1));
            break;
        }
    }

    QDateTime transTime = QDateTime::currentDateTime();

    if (!bReadRlt) {
        sError = QString("读卡失败");
        sFDMsg = QString("读卡失败\n请取卡后重新插卡");
        //  CDeviceFactory::GetAutoExTollScreen()->ShowHelp(AutoExTollScreen::HT_CardError,
        //                                              "读卡失败，请重新插卡");
        bReLoadCard = true;
        return false;
    }

    cpcIccInfo.MFef01.append((char *)&cpcBasicInfoRaw, sizeof cpcBasicInfoRaw);
    cpcIccInfo.MFef02.append((char *)&cpcBatteryRaw, sizeof cpcBatteryRaw);
    cpcIccInfo.cpcTollnfoRaw.append((char *)&cpcTollInfoRaw, sizeof cpcTollInfoRaw);
    cpcIccInfo.cpcpRoadInfoRaw.append((char *)&cpcRoadInfoRaw, sizeof cpcRoadInfoRaw);
    cpcIccInfo.cpcTollCellInfoRaw.append((char *)&cpcTollCellInfoRaw, sizeof cpcTollCellInfoRaw);

    cpcIccInfo.cpcTollCellInfo.Log();

    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
    if (!pCurTransInfo) {
        sError = QString("当前交易对象为空");
        DebugLog(QString("处理cpc卡时,交易对象为空"));
        return false;
    }

    if (cpcIccInfo.cpcBasicInfo.nBattery < 8) {
        DebugLog(QString("CPC卡%1电量%2不足")
                 .arg(QString::fromLatin1(cpcIccInfo.cpcBasicInfo.sCardID))
                 .arg(cpcIccInfo.cpcBasicInfo.nBattery));
        pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_CPCLowPower, true);
    }
    QString sMsg = QString("当前电量%1").arg(cpcIccInfo.cpcBasicInfo.nBattery);
    ShowMessage(sMsg);

    CVehEntryInfo vehEntryInfo;
    int nErrorCode = 0;
    bool bRlt = GetEntryInfoFromCardTollInfo(transTime, cpcIccInfo.cardTollInfo, vehEntryInfo,
                                             nErrorCode, sError);
    if (!bRlt) {
        bContinueOpenCard = false;
        sFDMsg = sError;
        bAlarm = true;
        return false;
    }

    if (!pCurTransInfo) {
        sError = QString("车道队列内无等待交易车辆");
        return false;
    }

    if (vehEntryInfo.EnTime.secsTo(transTime) > Ptr_Info->GetMaxDriveDays() * 3600 * 24) {
        DebugLog(QString("车辆行驶超过%1天,EntryTime:%2")
                 .arg(Ptr_Info->GetMaxDriveDays())
                 .arg(vehEntryInfo.EnTime.toString("yyyy-MM-dd hh:mm:ss")));

        //查询入口信息
        if (!QryOrSelectEntryInfo(transTime, pCurTransInfo)) {
            return false;
        }
    } else {
        pCurTransInfo->SetVehEntryInfo(vehEntryInfo, transTime);
    }

    if (isTruck(pCurTransInfo->VehInfo.VehClass)) {
        if (pCurTransInfo->vehEntryInfo.bVehState == VehState_BigTruck) {
            DebugLog(QString("车辆入口状态为大件车"));
            if (pCurTransInfo->VehInfo.GBVehType != UVT_BigTruck) {
                QString sTitle = QString("大件车");
                QString sMsg = QString("入口标记为大件车");
                QString sFDMsg = QString("等待确认车辆");
                if (ShowInformation_Help(sTitle, sMsg,
                                         QString("按【确认】键为大件车,【ESC】键取消"), true,
                                         sFDMsg, CSpEventMgr::SpEvent_Other, true)) {
                    SetInputVehType(UVT_BigTruck);
                    pCurTransInfo->SetGBVehType(UVT_BigTruck);
                }
            }
        }
    }

    pCurTransInfo->m_BeginTime = QDateTime::currentDateTime().toMSecsSinceEpoch();

    pCurTransInfo->SetCPCIccInfo(&cpcIccInfo);
    //界面显示入口信息
    FormVehInfo *pFrmVehInfo = (FormVehInfo *)m_pStateUI;
    pFrmVehInfo->ShowCPCIccInfo(cpcIccInfo);
    pFrmVehInfo->ShowEntryInfo(vehEntryInfo);

    //开始计费通行费
    int nDiscountType = 0;
    CFeeClass feeClass = FeeClass_Card;
    if (pCurTransInfo->vehEntryInfo.nEntryType != Entry_ByCard) {
        feeClass = pCurTransInfo->vehEntryInfo.sEnNetWorkIdHex == ORG_NETWORKID_HEX
                ? FeeClass_ProvCenter
                : FeeClass_MinistryCenter;
    }
    if (!CalculateTollFee(sError, sFDMsg, bAlarm, feeClass, nDiscountType, transTime)) {
        CDeviceFactory::GetAutoExTollScreen()->ShowHelp(AutoExTollScreen::HT_CheckError,
                                                        "信息核对失败，请转人工处理");
        return false;
    }
    //写卡
    CCardTollInfo cardTollInfo = pCurTransInfo->cpcIccInfo.cardTollInfo;
    cardTollInfo.sEnStationHex = Ptr_Info->GetHexStationID().right(4);
    cardTollInfo.sNetworkHex = ORG_NETWORKID_HEX;
    cardTollInfo.sEnLaneHex = Ptr_Info->GetHexLaneID().right(2);
    cardTollInfo.bLaneId = Ptr_Info->GetLaneId();
    cardTollInfo.szPassTime = transTime;
    cardTollInfo.bPassStatus = Ptr_Info->GetLaneType();
    cardTollInfo.bRsuMode = 0x02;
    bool bWriteRlt = pCardReader->WriteCPCCard(0, 0, &cardTollInfo, true, true);
    if (!bWriteRlt) bWriteRlt = pCardReader->WriteCPCCard(0, 0, &cardTollInfo, true, true);
    if (!bWriteRlt) {
        sError = QString("写出口信息失败");
        sFDMsg = QString("写卡失败\n请取卡重试");
        CDeviceFactory::GetAutoExTollScreen()->ShowHelp(AutoExTollScreen::HT_CardError,
                                                        "读卡失败，请重新插卡");
        return false;
    }
    //设置写卡成功
    pCurTransInfo->SetWriteCard_Exit(true);
    pCurTransInfo->m_endTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    bWriteRlt = pCardReader->CPCClear(true);
    if (!bWriteRlt) {
        DebugLog(QString("路径信息清除失败"));
    }
    pCardReader->CloseCard();
    pCardReader->GetPsamTermNo(pCurTransInfo->m_TermCode);

    QString sSubKey = transTime.toString("hhmmss");
    CStdLog::StdLog_CPCBaseInfo(sSubKey, pCurTransInfo->cpcIccInfo.cpcBasicInfo);
    CStdLog::StdLog_CPCStationInfo(sSubKey, pCurTransInfo->cpcIccInfo.cpcRoadInfo);
    CStdLog::StdLog_CPCFeeInfo(sSubKey, pCurTransInfo->cpcIccInfo.cpcTollCellInfo_After);

    Ptr_ETCCtrl->ChangeToVehMoneyState();
    bContinueOpenCard = false;
    return true;
}

bool CLaneState_VehInputExit::ProcessProCardEvent(int nReaderIndex, quint32 dwCardMac,
                                                  CCardReader *pCardReader, bool &bContinueOpenCard,
                                                  QString &sError, QString &sFDMsg, bool &bAlarm)
{
    CRsuIccInfo iccInfo;
    int nErrorCode = 0;
    bContinueOpenCard = true;
    DebugLog(QString("ProcessProCardEvent:%1").arg(nReaderIndex));

    bool bRlt = pCardReader->ReadProCard(&iccInfo.ProCardBasicInfo, &iccInfo.dwBalance,
                                         &iccInfo.CardTollInfo, iccInfo.Raw15, iccInfo.Raw19);
    if (!bRlt) {
        sError = QString("读ETC卡失败");
        sFDMsg = QString("读卡失败\n请重试");
        return false;
    }

    QString strRaw15 = Raw2HexStr((quint8 *)&iccInfo.Raw15, sizeof iccInfo.Raw15);
    QString strRaw19 = Raw2HexStr((quint8 *)&iccInfo.Raw19, sizeof iccInfo.Raw19);
    DebugLog(QString("读ETC卡,Raw15:%1,Raw19:%2").arg(strRaw15).arg(strRaw19));
    QString sCardId = QString::fromAscii(iccInfo.ProCardBasicInfo.szCardNo);
    CTransInfo hasTransInfo;
    bool bHasTrans = Ptr_ETCCtrl->CheckVehhasTransByCardId(sCardId, hasTransInfo);
    if (bHasTrans) {
        if (hasTransInfo.bTransOk()) {
            if (Ptr_ETCCtrl->CheckVehInVehQue(&hasTransInfo)) {
                sError = QString("该车已交易完成");
                return true;
            } else {  //本车道交易成功车辆，但没在过车队列中车辆，一般是后车放行前车，此时人工刷卡放行（人工道一般不存在这种情况）
                /*
                if (CheckCardHasTrans(iccInfo.CardTollInfo, false)) {
                    hasTransInfo.SetSaveResult(true);
                    Ptr_ETCCtrl->ReCompleteTrans_HaveTrans(DevIndex_Manual, &hasTransInfo);
                    OnTransFinished_Manual(true, 0, sError, &hasTransInfo);
                    return true;
                }*/
            }
        }
    }

    CTransInfo *pcurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
    if (!pcurTransInfo) {
        sError = QString("无等待交易车辆");
        bContinueOpenCard = true;
        return false;
    }
    pcurTransInfo->m_BeginTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    CTransInfo *pRsuTransInfo = Ptr_ETCCtrl->GetLastTransInfo(DevIndex_Second);

    //如果是本车道ETC交易失败，取最后的交易结果
    if (pRsuTransInfo->transState > CTransInfo::Ts_WaitOpResult) {
        if (pRsuTransInfo->dwOBUID > 0) {
            QString sLastCardId =
                    QString::fromAscii(pRsuTransInfo->IccInfo.ProCardBasicInfo.szCardNo);
            if (sCardId == sLastCardId && iccInfo.ProCardBasicInfo.wNetWorkId ==
                    pRsuTransInfo->IccInfo.ProCardBasicInfo.wNetWorkId) {
                /*暂不处理,避免过程中出现错误
                iccInfo.ef04Info = pRsuTransInfo->IccInfo.ef04Info;
                memcpy(&iccInfo.Ef04Raw, &pRsuTransInfo->IccInfo.Ef04Raw, sizeof
                iccInfo.Ef04Raw); pcurTransInfo->SetOBUBaseInfo(pRsuTransInfo->dwOBUID,
                &pRsuTransInfo->OBUBaseInfo);
                pcurTransInfo->SetOBUVehInfo(&pRsuTransInfo->OBUVehInfo, NULL);
                */
            }
        }
    }

    quint16 wNetworkId = iccInfo.ProCardBasicInfo.wNetWorkId;
    QString sLog = QString("ETC卡 %1,网络号:%2,卡号:%3,余额:%4")
            .arg(CCardFileConverter::GetCardTypeName(iccInfo.ProCardBasicInfo.bType))
            .arg(wNetworkId)
            .arg(sCardId)
            .arg(iccInfo.dwBalance);
    ShowLog(sLog);

    if (CheckCardHasTrans(iccInfo.CardTollInfo, false)) {
        // 1、判断是否是本车道已刷卡
        if (pcurTransInfo->transState == CTransInfo::Ts_WaitOpResult) {
            //当前交易已经是等待写卡结果状态.
            QString lastCardId =
                    QString::fromAscii(pcurTransInfo->IccInfo.ProCardBasicInfo.szCardNo);
            if (lastCardId == sCardId) {
                //当前刷卡，正是等待写卡结果的卡片，认为写卡成功
                DebugLog(QString("卡片%1上次刷卡失败,再次刷卡发现写卡成功").arg(sCardId));
                pcurTransInfo->SetTransState(CTransInfo::Ts_WaitMoney);
                pcurTransInfo->SetWriteCard_Exit(true);
                CProCardConsumeInfo lastConsumeInfo;
                pcurTransInfo->SetPayCardInfo(iccInfo.ProCardBasicInfo, lastConsumeInfo);
                pcurTransInfo->m_endTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
                Ptr_ETCCtrl->ChangeToVehMoneyState();
                bContinueOpenCard = false;
                pCardReader->CloseCard();
                return true;
            }
        }

        //提示重取tac后，生成fortac流水放行
        QString sTitle = QString("车辆已交易");
        QString sLastTransTime = iccInfo.CardTollInfo.szPassTime.toString("MM-dd hh:mm:ss");
        QString sMessage = QString("该卡%1于收费站:%2,车道:%3已交易,请核实")
                .arg(sLastTransTime)
                .arg(Ptr_Info->GetStationName())
                .arg(iccInfo.CardTollInfo.bLaneId);

        if (0 != nReaderIndex) {  //由人工在桌面读写器上处理
            bAlarm = true;
            sFDMsg = QString("该卡已交易\n等待人工处理");

            return false;
        }
        if (!ShowInformation_Help(sTitle, sMessage, QString("按<确认>键车辆放行,<ESC>键返回"),
                                  bAlarm, QString("等待人工确认"), CSpEventMgr::SpEvent_Other,
                                  true)) {
            return false;
        }
        pcurTransInfo->SetIccInfo(&iccInfo);
        pcurTransInfo->mediaType = MediaType_OBU;
        bool getRlt = ReGetTac(pCardReader, pcurTransInfo, sError, sFDMsg);
        if (!getRlt) {
            DebugLog(sError);
            bContinueOpenCard = true;
            return false;
        }
        return getRlt;
    }
    pcurTransInfo->SetIccInfo(&iccInfo);
    pcurTransInfo->mediaType = MediaType_OBU;
    int nDiscountType = 0;
    bool bEmVeh = CheckEmVehList(pcurTransInfo, nDiscountType);
    if (!bEmVeh) {
        QString sOBUId;
        int nBListType = 0;
        int nQryBRlt = 0;
        if (Ptr_Info->bCheckCardBList()) {
            QString sDesc;
            nQryBRlt = CParamFileMgr::QryCardBList(2, sOBUId, sCardId, wNetworkId, nBListType,
                                                   sDesc, sError);
        }
        if (0 != nQryBRlt) {
            if (1 == nQryBRlt)
                nErrorCode = CSpEventMgr::SpEvent_BlackOBU;
            else if (2 == nQryBRlt)
                nErrorCode = CSpEventMgr::GetEventIdByCardBListType(nBListType);

            /*//为了便于远程控制，此处不论桌面读写器还是卡机外置天线，都弹框提示处理,所以注掉读写器index判断
            if (nReaderIndex != 0) {
                bContinueOpenCard = true;
                bAlarm = true;
                sFDMsg = QString("黑名单卡\n等待人工处理");
                return false;
            }*/
            pcurTransInfo->m_bBlackCard = true;
            sFDMsg = QString("黑名单卡");
            if (!ShowInformation_Help(QString("黑名单卡"), sError,
                                      QString("按<确定>继续,<取消>返回"), false, sFDMsg, nErrorCode,
                                      true)) {
                bContinueOpenCard = true;
                return false;
            }
            pcurTransInfo->SetOncePaySpEvent(OncePay_Sp_CardInBList, true);
        }
    }

    bool bCheckRlt =
            VerifyProCardBasicInfo_Light(DevIndex_Manual, pcurTransInfo, iccInfo.ProCardBasicInfo,
                                         bEmVeh, pcurTransInfo->VehInfo, nErrorCode, sError);
    if (!bCheckRlt) {
        if (nErrorCode == CSpEventMgr::SpEvent_NotSelfCard) {
            if (!ShowInformation_Help(QString("非本车卡"), sError,
                                      QString("按<确定>继续,<取消>返回"), false, sFDMsg, nErrorCode,
                                      true)) {
                bContinueOpenCard = true;
                return false;
            }
        } else
            return false;
    }
    /*
    if (pcurTransInfo->VehInfo.VehClass != iccInfo.ProCardBasicInfo.bVehClass) {
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sError = QString("卡内车型与输入车型不符");
        return false;
    }*/

    QDateTime TransTime = QDateTime::currentDateTime();
    CVehEntryInfo vehEnInfo;

    if (!GetEntryInfoFromCardTollInfo(TransTime, iccInfo.CardTollInfo, vehEnInfo, nErrorCode,
                                      sError)) {
        // pcurTransInfo->SetOncePaySpEvent(OncePay_Sp_CardEntryError);
        return false;
    } else {
        // pcurTransInfo->SetOncePaySpEvent(OncePay_Sp_CardEntryError, false);
    }

    if (vehEnInfo.EnTime.secsTo(TransTime) > Ptr_Info->GetMaxDriveDays() * 3600 * 24) {
        DebugLog(QString("车辆行驶超过%1天,EntryTime:%2")
                 .arg(Ptr_Info->GetMaxDriveDays())
                 .arg(vehEnInfo.EnTime.toString("yyyy-MM-dd hh:mm:ss")));

        //查询入口信息
        if (!QryOrSelectEntryInfo(TransTime, pcurTransInfo)) {
            return false;

        }
    } else {
        pcurTransInfo->SetVehEntryInfo(vehEnInfo, TransTime);
    }

    if (pcurTransInfo->VehInfo.GBVehType == UVT_Normal)
        pcurTransInfo->SetGBVehType(
                    GetGBVehTypeByUserType(pcurTransInfo->IccInfo.ProCardBasicInfo.bUserType));

    if (isTruck(pcurTransInfo->VehInfo.VehClass)) {
        if (pcurTransInfo->vehEntryInfo.bVehState == VehState_BigTruck) {
            if (pcurTransInfo->VehInfo.GBVehType != UVT_BigTruck) {
                QString sTitle = QString("大件车");
                QString sMsg = QString("入口为大件车");
                QString sFDMsg = QString("等待确认车辆");
                if (ShowInformation_Help(sTitle, sMsg,
                                         QString("按【确认】键为大件车,【ESC】键取消"), true,
                                         sFDMsg, CSpEventMgr::SpEvent_Other, true)) {
                    SetInputVehType(UVT_BigTruck);
                    pcurTransInfo->SetGBVehType(UVT_BigTruck);
                }
            }
        }
    }

    bool bYJVeh = CCardFileConverter::IsYJCard(iccInfo.ProCardBasicInfo);

    if (CCardFileConverter::IsArmyCard(iccInfo.ProCardBasicInfo.szNetworkId)) {
        pcurTransInfo->SetGBVehType(UVT_Army);

    } else {
        if (bYJVeh) {
            pcurTransInfo->SetGBVehType(UVT_YJVeh);
        }
    }

    if (pcurTransInfo->VehInfo.GBVehType == UVT_Normal)
        pcurTransInfo->VehInfo.GBVehType =
                GetGBVehTypeByUserType(pcurTransInfo->IccInfo.ProCardBasicInfo.bUserType);

    bool bLocalEntry = vehEnInfo.sEnNetWorkIdHex == QString("3601");
    CFeeClass feeClass = bLocalEntry ? FeeClass_ProvCenter : FeeClass_MinistryCenter;

    if (!CalculateTollFee(sError, sFDMsg, bAlarm, feeClass, nDiscountType, TransTime)) {
        ShowErrorMessage(sError);
        return false;
    }

    pcurTransInfo->etcTransType = TransType_ExOnlyAppTrans;
    pCardReader->GetPsamTermNo(pcurTransInfo->m_TermCode);
    if (iccInfo.ProCardBasicInfo.bType == CARD_TYPE_STORE_CARD) {
        if (iccInfo.dwBalance < pcurTransInfo->m_nTransFee) {
            pcurTransInfo->SetOncePaySpEvent(OncePay_Sp_CardBalanceLow);
        }
    }
    if (0 == iccInfo.dwBalance) pcurTransInfo->SetOncePaySpEvent(OncePay_Sp_CardNoBalance);
    pcurTransInfo->SetTransState(CTransInfo::Ts_WaitOpResult);

    CPro0019Raw_NewGB Raw0019;
    pcurTransInfo->GetPro0019RawForExit(Raw0019);
    CProCardConsumeInfo ProCardConsumeInfo;
    bool bWriteRlt = pCardReader->WriteExitInfo(dwCardMac, &Raw0019.File0012Raw_GB,
                                                ProCardConsumeInfo, iccInfo.ProCardBasicInfo.bSM4);
    if (!bWriteRlt) {
        DebugLog(QString("ETC卡%1写卡失败,重试一次").arg(sCardId));
        SleeperThread::msleep(50);
        bWriteRlt = pCardReader->WriteExitInfo(dwCardMac, &Raw0019.File0012Raw_GB,
                                               ProCardConsumeInfo, iccInfo.ProCardBasicInfo.bSM4);
        if (!bWriteRlt) {
            DebugLog(QString("ETC卡%1写卡失败").arg(sCardId));
            sError = QString("写卡失败,请重试");
            sFDMsg = QString("写卡失败\n请重试");
            return false;
        }
    }
    DebugLog(QString("ETC卡%1写卡成功").arg(sCardId));
    bContinueOpenCard = false;
    pCardReader->CloseCard();
    pcurTransInfo->SetPayCardInfo(iccInfo.ProCardBasicInfo, ProCardConsumeInfo);

    pcurTransInfo->SetWriteCard_Exit(true);
    pcurTransInfo->SetTransState(CTransInfo::Ts_WaitMoney);
    pcurTransInfo->m_endTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    Ptr_ETCCtrl->ChangeToVehMoneyState();
    return true;
}

bool CLaneState_VehInputExit::ReProcessCalcFee()
{
    CVehInfo vehInfo;
    CVehAxisInfo vehAxisInfo;
    QString sError, sFDMsg;
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
    if (!pCurTransInfo) {
        DebugLog(QString("交易对像为空"));
        return false;
    }
    bool bRlt = GetVehInfoForCPC_New(true, pCurTransInfo, vehInfo, vehAxisInfo, sError, sFDMsg);
    if (!bRlt) {
        return false;
    }

    CFeeClass feeClass = FeeClass_Card;
    bool bDiscount = false;
    if (pCurTransInfo->mediaType == MediaType_OBU) {
        feeClass = FeeClass_MinistryCenter;
        bDiscount = true;
    }
    bool bAlarm = false;
    int nDiscountType = 0;
    if (!CalculateTollFee(sError, sFDMsg, bAlarm, feeClass, nDiscountType,
                          pCurTransInfo->TransTime)) {
        ShowErrorMessage(sError);
        return false;
    }
    Ptr_ETCCtrl->ChangeToVehMoneyState();
    return true;
}

bool CLaneState_VehInputExit::CalculateTollFee(QString &sError, QString &sFDMsg, bool &bAlarm,
                                               CFeeClass actureFeeClass, int nDiscountType,
                                               const QDateTime &TransTime)
{
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
    if (pCurTransInfo == 0) {
        sError = "车道队列内无未交易车辆";
        return false;
    } else if (pCurTransInfo->vehEntryInfo.nEntryType == Entry_None) {
        sError = "无入口信息";
        sFDMsg = QString("无入口信息\n等待人工处理");
        bAlarm = true;
        return false;
    }

    int nErrorCode = 0;
    bool bEmVeh = pCurTransInfo->m_bEmVeh;
    if (!bEmVeh) {
        bEmVeh = CheckEmVehList(pCurTransInfo, nDiscountType);
    }

    QString scurGantryHex;
    bool bOpenGantry = false;
    if (!CTollGantryMgr::GetTollGantryMgr()->GetCurGantryInfo(
                scurGantryHex, pCurTransInfo->m_curGantryInfo, bOpenGantry)) {
        ErrorLog(QString("门架字典内门架为空"));
        nErrorCode = CSpEventMgr::SpEvent_Other;
        sError = QString("门架字典为空,计费异常");
        return false;
    }

    bool bIsLocal = false;
    CFeeClass feeClass = actureFeeClass;

    //入口信息，通过读卡得到的，需要比较卡内信息
    if (pCurTransInfo->vehEntryInfo.nEntryType == Entry_ByCard) {
        //判断是否是本省
        bIsLocal = (pCurTransInfo->vehEntryInfo.sEnNetWorkIdHex == ORG_NETWORKID_HEX);
        if (pCurTransInfo->mediaType == MediaType_CPC) {
            bIsLocal = bIsLocal && (1 >= pCurTransInfo->cpcIccInfo.cpcRoadInfo.nProvCnt);
        }

        QString sCompareTitle, sCompareDisp;
        UpdateVehInfo_ByEntryInfo(pCurTransInfo->VehInfo, pCurTransInfo->vehEntryInfo.bEnVC);
        if (pCurTransInfo->vehEntryInfo.bEnVC != pCurTransInfo->VehInfo.VehClass) {
            // 检查是否为OBU且用户类型不为0，如果是则跳过车型对比
            bool bSkipVehClassCheck = false;
            if (pCurTransInfo->mediaType == MediaType_OBU &&
                pCurTransInfo->IccInfo.ProCardBasicInfo.bUserType != 0) {
                bSkipVehClassCheck = true;
                DebugLog(QString("OBU用户类型为%1，跳过车型对比")
                         .arg(pCurTransInfo->IccInfo.ProCardBasicInfo.bUserType));
            }

            if (!bSkipVehClassCheck) {
                sCompareTitle = "车型";
                sCompareDisp = QString("入口:%1,出口:%2")
                        .arg(GetVehClassName(pCurTransInfo->vehEntryInfo.bEnVC))
                        .arg(GetVehClassName(pCurTransInfo->VehInfo.VehClass));
                DebugLog(QString("入出车型不符,入:%1,出:%2")
                         .arg(pCurTransInfo->vehEntryInfo.bEnVC)
                         .arg(pCurTransInfo->VehInfo.VehClass));

                //如出口车型不符
                sCompareTitle = sCompareTitle + QString("不符");
                if (!ShowInformation_Help(sCompareTitle, QString("%1,是否继续计费?").arg(sCompareDisp),
                                          QString("按<确认>键按出口车型计费,按<取消>键重新输入"), true,
                                          sCompareTitle, CSpEventMgr::SpEvent_VehClassDiff, true)) {
                    return false;
                }
                DebugLog(QString("入出车型信息不符,转在线计费"));
                if (feeClass != FeeClass_Min)
                    feeClass = bIsLocal ? FeeClass_ProvCenter : FeeClass_MinistryCenter;
            }
        }

        if (pCurTransInfo->vehEntryInfo.bEnVT == UVT_TRUCK ||
                pCurTransInfo->vehEntryInfo.bEnVT == UVT_BigTruck) {
            if (pCurTransInfo->VehInfo.GBVehType == UVT_Normal) {
                sCompareDisp =
                        QString("入口:%1,出口:%2,是否继续计费?")
                        .arg(GetUnionVehTypeName((CUnionVehType)pCurTransInfo->vehEntryInfo.bEnVT))
                        .arg(GetUnionVehTypeName(pCurTransInfo->VehInfo.GBVehType));
                bool bContinue =
                        ShowInformation_Help(QString("车种不符"), sCompareDisp,
                                             QString("按<确认>键按出口车种计费,<取消>键重新输入"), true,
                                             QString("车种不符"), CSpEventMgr::SpEvent_Other, true);
                if (!bContinue) {
                    return false;
                }
                DebugLog(QString("入出口车种不符,入口:%1,出口:%2")
                         .arg(pCurTransInfo->vehEntryInfo.bEnVT)
                         .arg(pCurTransInfo->VehInfo.GBVehType));
            }
        }
        if (!pCurTransInfo->CheckVehEntryAxisNum(sCompareDisp)) {
            QString sName = GetUnionVehTypeName(pCurTransInfo->VehInfo.GBVehType);
            sCompareTitle = QString("%1 入出轴数不符").arg(sName);

            if (!ShowInformation_Help(sCompareTitle, QString("%1,是否继续计费?").arg(sCompareDisp),
                                      QString("按<确认>键按出口轴数计费,按<取消>键重新输入"), true,
                                      sCompareTitle, CSpEventMgr::SpEvent_VehClassDiff, true)) {
                return false;
            }
            if (feeClass != FeeClass_Min) {
                feeClass = bIsLocal ? FeeClass_ProvCenter : FeeClass_MinistryCenter;
            }
        }

        bool bSamePlate =
                CompareVehPlateAndResetVehInfo(pCurTransInfo, sCompareTitle, sCompareDisp);

        if (!bSamePlate) {
            if (!ShowInformation_Help(sCompareTitle, QString("%1,是否继续计费?").arg(sCompareDisp),
                                      QString("按<确认>键按出口车牌计费,按<取消>键重新输入"), true,
                                      sCompareTitle, CSpEventMgr::SpEvent_VehPlateDiff, true)) {
                return false;
            }
            DebugLog(QString("入出车牌信息不符,转在线计费"));
            if (feeClass != FeeClass_Min)
                feeClass = bIsLocal ? FeeClass_ProvCenter : FeeClass_MinistryCenter;
        }

    } else {
        //入口信息来源于查询或指定的
        bIsLocal = pCurTransInfo->vehEntryInfo.sEnNetWorkIdHex == ORG_NETWORKID_HEX;
        if (feeClass != FeeClass_Min) {
            feeClass = bIsLocal ? FeeClass_ProvCenter : FeeClass_MinistryCenter;
        }
    }

    //节假日免费也改到计费时处理
    bool bFreeVeh = IsFreeVehType_GB(pCurTransInfo->VehInfo.GBVehType);
    bool bManNum8 = false;
    bool bHolidayFree = HolidayFreeMenu(pCurTransInfo->VehInfo.VehClass, bManNum8);

    pCurTransInfo->bHolidayFree = bHolidayFree ? 1 : 0;
    pCurTransInfo->bManNum8 = bManNum8;
    if (bHolidayFree) {
        DebugLog(QString("节假日免费车辆,车型：%1").arg(pCurTransInfo->VehInfo.VehClass));
    }
    if (pCurTransInfo->vehEntryInfo.nEntryType == Entry_ByCard) {
        //如果是卡内计费，需要算门架
        if (MediaType_CPC == pCurTransInfo->mediaType ||
                MediaType_OBU == pCurTransInfo->mediaType) {
            QString sExStationHex = Ptr_Info->GetHexStationID();
            if (CTollGantryMgr::GetTollGantryMgr()->CheckNeedGrantryCalcFee()) {
                QString sMsg;
                if (!CFareCalcUnit::CalcGantryFee(*pCurTransInfo, pCurTransInfo->VehInfo.VehClass,
                                                  sExStationHex, pCurTransInfo->bHolidayFree,
                                                  pCurTransInfo->TransTime, sMsg)) {
                    nErrorCode = CSpEventMgr::SpEvent_EntryNoFee;
                    pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_FeeQryFeeFailed);
                    sError = QString("门架计费失败,Error:%1").arg(sMsg);
                    sFDMsg = QString("计费失败\n等待人工处理");
                    DebugLog(sError);
                    bAlarm = true;
                    return false;
                }
            }
        }
    }
    //综合判断是否是免费车
    bool bFree = bEmVeh || bFreeVeh || bHolidayFree;

    CAllRoadMinFeeInfo MinFeeInfo;

    bool bETC = pCurTransInfo->mediaType == MediaType_OBU;
    if (!QryMinFeeInfo(pCurTransInfo->VehInfo.VehClass, pCurTransInfo->vehEntryInfo, bETC,
                       MinFeeInfo, nErrorCode, sError)) {
        sError = QString("最短路径费率查询失败");
        sFDMsg = QString("计费异常\n等待人工处理");
        bAlarm = true;
        DebugLog(sError);
        return false;
    }
    CAllRoadMinFeeInfo MinFeeInfo1;
    if (pCurTransInfo->VehInfo.bNeedConfirmAxis()) {
        if (!QryMinFeeInfo(VC_Truck1, pCurTransInfo->vehEntryInfo, bETC, MinFeeInfo1, nErrorCode,
                           sError)) {
            sError = QString("货1最短路径费率查询失败");
            sFDMsg = QString("计费异常\n等待人工处理");
            DebugLog(sError);
            bAlarm = true;
            return false;
        }
    }

    pCurTransInfo->SetAllRoadMinFeeInfo(MinFeeInfo, &MinFeeInfo1);
    qint32 nPercent = 0;

    bool isU = false;  //是否是U型车

    QString sHex =
            pCurTransInfo->vehEntryInfo.sEnNetWorkIdHex + pCurTransInfo->vehEntryInfo.sEnStationHex;
    isU = Ptr_Info->IsUVeh(sHex) && bIsLocal;
    pCurTransInfo->m_bUReturn = isU;

    if (pCurTransInfo->vehEntryInfo.nEntryType == Entry_ByCard) {
        if (isU) {
            if (Ptr_Info->bStopUJ()) {
                QString sMsg =
                        QString("U型车,行驶时间%1").arg(pCurTransInfo->vehEntryInfo.nPassTime);
                bool bDoU = ShowInformation_Help(
                            QString("U型车"), sMsg, QString("按<确认>键继续计费,按<取消>键返回"), true,
                            QString("U型车"), CSpEventMgr::SpEvent_Other, true);
                if (!bDoU) {
                    return false;
                }
            }
        } else {
            /*
            if (bIsLocal) {
                CFlagListInfo flagListInfo;
                bool bETC = pCurTransInfo->mediaType == MediaType_OBU;
                bool bFlag = QueryFlagInfo(pCurTransInfo, bETC, flagListInfo);
                if (bFlag) {
                    QString sMsg = QString("车辆经过调头点%1").arg(flagListInfo.UturnPoint);
                    bool bDoJ = ShowInformation_Help(
                        QString("J型车"), sMsg, QString("按<确认>键继续计费,按<取消>键返回"), true,
                        QString("J型车"), CSpEventMgr::SpEvent_Other, true);
                    if (!bDoJ) {
                        return false;
                    }
                }
            }*/
        }
    }

    // cpc 计费不会返回最短路径，只有在线计费失败后才会返回最短路径
    bool bCalcResult = false;
    while (!bCalcResult) {
        switch (feeClass) {
        case FeeClass_OBU: {
            pCurTransInfo->SumCalcMoney_Exit(feeClass, bFree, true, nDiscountType);
            qint32 totalFee = 0;
            qint32 totalRealFee = 0, nCardCost = 0;
            pCurTransInfo->GetTollMoney_Exit(totalFee, totalRealFee, nCardCost);

            CFeeClass lastFeeClass = feeClass;
            CheckFeeClass_Etc_New(pCurTransInfo, totalRealFee, lastFeeClass, isU, bIsLocal, bFree,
                              sError);
            if (lastFeeClass == feeClass) {
                bCalcResult = true;
            } else {
                feeClass = lastFeeClass;
            }

            break;
        }
        case FeeClass_Card: {
            //
            bool bRlt = CheckCPCRoadInfo(pCurTransInfo->cpcIccInfo.cpcRoadInfo,
                                         pCurTransInfo->cpcIccInfo.cpcTollCellInfo,
                                         pCurTransInfo->bIsJC);
            if (!bRlt) {
                pCurTransInfo->cpcIccInfo.cpcTollCellInfo.bLostTollCell = true;
                DebugLog(QString("CPC卡内路径信息不全,转在线计费"));
                feeClass = bIsLocal ? FeeClass_ProvCenter : FeeClass_MinistryCenter;
                break;
            }
            pCurTransInfo->SumCalcMoney_Exit(feeClass, bFree, false, nDiscountType);
            qint32 totalFee = 0;
            qint32 totalRealFee = 0, nCardCost = 0;
            pCurTransInfo->GetTollMoney_Exit(totalFee, totalRealFee, nCardCost);
            CFeeClass lastFeeClass = feeClass;
            CheckFeeClass_Etc_New(pCurTransInfo, totalRealFee, lastFeeClass, isU, bIsLocal, bFree,
                              sError);
            if (feeClass == lastFeeClass) {
                bCalcResult = true;
            } else {
                feeClass = lastFeeClass;
            }

            break;
        }
        case FeeClass_Min: {
            bool bETCTrans = pCurTransInfo->mediaType == MediaType_OBU;
            pCurTransInfo->SumCalcMoney_Exit(feeClass, bFree, bETCTrans, nDiscountType);
            bCalcResult = true;
            break;
        }
        case FeeClass_ProvCenter:
        case FeeClass_MinistryCenter: {
            //在线计费
            if (pCurTransInfo->MinFeeInfo.bJcSpFee) {
                DebugLog(QString("在线计费,因为当前为机场特殊计费,不进行后续判断。"));
                feeClass = FeeClass_Min;
                break;
            }
            CFareQryCondition qryCondition;
            CFareQryResult fareResult;
            bool bDiscount = pCurTransInfo->mediaType == MediaType_OBU;
            bool bQryRlt = pCurTransInfo->QryFare_OnLine_Union(qryCondition, fareResult, sError,
                                                               bDiscount, TransTime);
            if (!bQryRlt) {
                /*
                        bool bRlt = CMessageBox::Information_Help(
                            QString("在线计费失败"), QString("是否按最小费率计费"),
                            QString("按【确定】键按最小费率计费【取消】键返回"));
                        if (!bRlt) {
                            return false;
                        }*/
                DebugLog(QString("在线计费失败,转最小费额计费"));
                feeClass = FeeClass_Min;
            } else {
                pCurTransInfo->SetFareQryResult(qryCondition, fareResult);
                bool bETCTrans = pCurTransInfo->mediaType == MediaType_OBU;

                if (fareResult.actualFeeClass == 4 || fareResult.actualFeeClass == 5)
                    feeClass = (CFeeClass)fareResult.actualFeeClass;
                pCurTransInfo->SumCalcMoney_Exit(feeClass, bFree, bETCTrans, nDiscountType);
                // bCalcResult=true;
                qint32 totalFee = 0;
                qint32 totalRealFee = 0, nCardCost = 0;
                pCurTransInfo->GetTollMoney_Exit(totalFee, totalRealFee, nCardCost);

                if (CheckMaxFee(pCurTransInfo->VehInfo.VehClass,
                                pCurTransInfo->VehInfo.GBVehType, totalRealFee)) {
                    //因为在线计费已经比较了最小费额，所以不用再比较了
                    bCalcResult = true;

                    if (fareResult.actualFeeClass == FeeClass_ProvCenter && bIsLocal) {
                        if (fareResult.FeeCalcResult == 1) {
                            DebugLog("在线计费返回最终计费结果已经比较收费上限，不再做比较");
                            pCurTransInfo->m_nCalcMaxFee = 1;  //只是标记进行最大费额计算
                        }
                    }

                    break;
                    /*
                                                    CFeeClass lastFeeClass = feeClass;

                                                    if (fareResult.actualFeeClass ==
                               FeeClass_ProvCenter
                               && bIsLocal) { if (fareResult.FeeCalcResult == 1) {
                                                            DebugLog("在线计费返回最终计费结果已经比较收费上限，不再做比较");
                                                            bCalcResult = true;
                                                            break;
                                                        }
                                                    }
                                                    bool bCheckResult =
                                                        CheckFeeClass_Etc_New(pCurTransInfo,
                               totalRealFee, lastFeeClass, isU, bIsLocal, bFree, sError); if
                               (!bCheckResult) { DebugLog(
                                                            QString("在线计费返回结果超出最小费额2倍或1.5倍,转最小费额计费"));
                                                        feeClass = FeeClass_Min;
                                                    } else {
                                                        if (lastFeeClass == feeClass)
                                                            bCalcResult = true;
                                                        else
                                                            feeClass = lastFeeClass;
                                                    }
                                                    break;*/
                } else {
                    DebugLog(QString("在线计费返回结果%1超过允许最大额,转最小费额")
                             .arg(totalRealFee));
                    feeClass = FeeClass_Min;
                }
            }
            break;
        }
        default:
            return false;
            break;
        }
    }
    if (bCalcResult) {
        bool bOutTime = pCurTransInfo->CheckOutTime(
                    sError);  // CheckOutTime(pCurTransInfo, sError, Ptr_Info->GetMaxOutTime());
        if (bOutTime) {
            bool bRlt = ShowInformation_Help(
                        QString("超时"), sError, QString("按<确定>键继续<取消>键返回"), true,
                        QString("车辆超时\n等待人工处理"), CSpEventMgr::SpEvent_OverTime, true);
            if (!bRlt) {
                return false;
            }
        }
        qint32 totalFee = 0;
        qint32 totalRealFee = 0, nCardCost = 0;
        pCurTransInfo->GetTollMoney_Exit(totalFee, totalRealFee, nCardCost);
        //扣费金额为0
        pCurTransInfo->SetLastMoney(totalFee, totalRealFee, 0, feeClass, nPercent);

        //
        if (totalRealFee > 0 && bIsLocal) {
            bool bDiffFee = pCurTransInfo->ProcessDiffFeeInfo(bIsLocal);
            if (bDiffFee) {
                DebugLog(QString("差异化计费处理成功"));
            }
        } else {
            pCurTransInfo->ClearFeeInfoL();
        }
    }
    return bCalcResult;
}

/**
 * @brief CLaneState_VehInputExit::DoBadCard 处理坏卡键
 * @return
 */
bool CLaneState_VehInputExit::DoBadCard(QString &sErrorMsg)
{
    if (!IfInputVehInfoFinished()) {
        sErrorMsg = "请输入车辆信息";
        return false;
    }

    if (!Ptr_ETCCtrl->bHasWaitingTransVeh(DevIndex_Manual)) {
        sErrorMsg = "车道队列内无未交易车辆";
        return false;
    }

    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
    if (!pCurTransInfo) {
        sErrorMsg = "车道队列内无未交易车辆";
        return false;
    }
    if (pCurTransInfo->hasWriteCard_Exit()) {
        QString sCardId;
        if (pCurTransInfo->mediaType == MediaType_OBU)
            sCardId = QString::fromAscii(pCurTransInfo->IccInfo.ProCardBasicInfo.szCardNo);
        else if (pCurTransInfo->mediaType == MediaType_CPC)
            sCardId = QString::fromAscii(pCurTransInfo->cpcIccInfo.cpcBasicInfo.sCardID);
        sErrorMsg = QString("当前车辆已刷卡,卡号[%1]").arg(sCardId);
        return false;
    }

    //填充车辆信息
    CVehInfo vehInfo;
    CVehAxisInfo vehAxisInfo;
    QString sFDMsg;
    bool bRlt = GetVehInfoForCPC_New(true, pCurTransInfo, vehInfo, vehAxisInfo, sErrorMsg, sFDMsg);
    if (!bRlt) {
        return false;
    }

    QString sVLp = GB2312toUnicode(pCurTransInfo->VehInfo.szVehPlate);
    int nPlateColor = pCurTransInfo->VehInfo.nVehPlateColor;

    CTransInfo transInfo;
    bool bHaveTrans = Ptr_ETCCtrl->CheckVehhasTransByVLP(sVLp, transInfo);
    if (bHaveTrans) {
        if (transInfo.bTransOk()) {
            sErrorMsg = QString("%1本车道已交易,%2分钟之内不允许进行二次交易")
                    .arg(sVLp)
                    .arg(Ptr_Info->GetMaxInterval() / 60);
            DebugLog(sErrorMsg);
            return false;
        }
    }

    int nErrorCode = 0;
    int nRlt = CheckVehList(DevIndex_Manual, sVLp, nPlateColor, nErrorCode, sErrorMsg);
    if (nRlt > 0) {
        if (!CMessageBox::Information_Help(QString("黑名单车辆"), sErrorMsg,
                                           QString("<确定>键继续,<取消>键返回"), false)) {
            sFDMsg = QString("黑名单车辆");
            return false;
        }
        if (3 == nRlt) {
            nRlt = CFuncMenu::ShowMenu_PrePayBList();
            if (0 == nRlt) return false;
            pCurTransInfo->m_nPrepayList = nRlt;
        }
    }

    //输入坏卡卡号
    FormInputCardNo dlgInputCardNo(GetMainDlg());
    if (!dlgInputCardNo.InputCardNo(FormInputCardNo::CardNoType_Bad, 20)) {
        return false;
    }
    QString sCardNo = dlgInputCardNo.GetInputResult();
    QDateTime transTime = QDateTime::currentDateTime();

    //根据卡号，查询入口记录
    CEntryQryCondition condition;
    int nCardNoLen = sCardNo.length();
    int nMinLen = qMin(sCardNo.toAscii().length(), 20);

    if (16 == nCardNoLen) {
        condition.mediaType = MediaType_CPC;
        //填充坏卡卡号
        pCurTransInfo->mediaType = MediaType_CPC;
        memcpy(pCurTransInfo->cpcIccInfo.cpcBasicInfo.sCardID, sCardNo.toAscii().data(), nMinLen);

    } else if (20 == nCardNoLen) {
        condition.mediaType = MediaType_OBU;
        pCurTransInfo->mediaType = MediaType_OBU;
        QString sNetWorkId;
        char IssueId[20];
        int nCardType = CCardFileConverter::GetProCardTypeByNumber(sCardNo, sNetWorkId, IssueId);

        if (nCardType != CARD_TYPE_STORE_CARD && nCardType != CARD_TYPE_TALLY_CARD) {
            bool bRlt = CMessageBox::Information_Help("提示", "ETC卡号错误",
                                                      QString("按[确认]键继续处理,[ESC]返回"),
                                                      CMessageBox::Style_OkCancel, NULL, true);
            if (!bRlt) return false;
            DebugLog(QString("输入ETC卡号:%1错误,按记账卡处理").arg(sCardNo));
            nCardType = CARD_TYPE_TALLY_CARD;
        }
        if (nCardType > 0) {
            memcpy(pCurTransInfo->IccInfo.ProCardBasicInfo.szCardNo,
                   sCardNo.right(16).toAscii().data(), 16);
            memcpy(pCurTransInfo->IccInfo.ProCardBasicInfo.szNetworkId, sNetWorkId.toAscii(), 4);
            pCurTransInfo->IccInfo.ProCardBasicInfo.wNetWorkId = sNetWorkId.toInt();
            pCurTransInfo->IccInfo.ProCardBasicInfo.bType = nCardType;
            CProCardConsumeInfo cardConsumeInfo;
            pCurTransInfo->SetPayCardInfo(pCurTransInfo->IccInfo.ProCardBasicInfo, cardConsumeInfo);
        }
    } else {
        ShowErrorMessage("cpc卡输入16位ETC卡输入20位卡号");
        return false;
    }
    if (condition.mediaType == MediaType_CPC) {
        condition.mediaNo = sCardNo;
    } else if (condition.mediaType == MediaType_OBU) {
        condition.cardId = sCardNo;
    }
    condition.vehicleId = QString("%1_%2")
            .arg(GB2312toUnicode(pCurTransInfo->VehInfo.szVehPlate))
            .arg(pCurTransInfo->VehInfo.nVehPlateColor);
    // QDateTime beginTime = transTime.addDays(-7);
    //  QDateTime endTime = transTime.addSecs(-600);

    //不知道入出时间可以填空
    condition.enStartTime = "";  // beginTime.toString("yyyy-MM-ddThh:mm:ss");
    condition.enEndTime = "";    // endTime.toString("yyyy-MM-ddThh:mm:ss");
    condition.exStationId = Ptr_Info->GetGBStationId();
    condition.exTollLaneId = Ptr_Info->GetGBLaneId();

    QList<CEntryQryResult> entryList;
    bool bQryResult = CFareCalcUnit::QryEntry_OnLine(condition, entryList);
    bool bQryEntry = false;
    CEntryQryResult qryEntry;
    if (bQryResult && entryList.size() > 0) {
        //查询出的结果中，用此入口信息去做在线计费
        CEntryQryResult *pResult = CFuncMenu::ShowMenu_qryEntryStation(entryList);
        if (pResult) {
            qryEntry = *pResult;
            bQryEntry = true;
        }
    }
    if (bQryEntry) {
        if (!fillEntryInfoByQry(pCurTransInfo, qryEntry, transTime, sErrorMsg)) {
            ShowErrorMessage(sErrorMsg);
            return false;
        }
    } else {
        //未找到入口信息，选站后，使用最小费率计费
        FormSelectStation dlgStation(GetMainDlg());
        COrgBasicInfo stationInfo, laneInfo;
        if (!dlgStation.SelectStation(stationInfo, laneInfo)) {
            return false;
        }
        //通过选择，填充入口信息
        fillEntryInfoBySelStation(pCurTransInfo, stationInfo, laneInfo, transTime);
        if (0 == pCurTransInfo->vehEntryInfo.nEnLaneID) {
            pCurTransInfo->mediaType = MediaType_None;  //手工选择入口,按无介质处理
            DebugLog(QString("坏卡按无介质处理"));
        }
    }

    bool bAlarm = false;
    bool bLocalEntry = pCurTransInfo->vehEntryInfo.sEnNetWorkIdHex == QString("3601");
    CFeeClass feeClass = bLocalEntry ? FeeClass_ProvCenter : FeeClass_MinistryCenter;
    int nDiscountType = 0;
    if (!CalculateTollFee(sErrorMsg, sFDMsg, bAlarm, feeClass, nDiscountType, transTime)) {
        pCurTransInfo->ClearTransInfo();
        return false;
    }
    pCurTransInfo->m_bBadCard = true;
    DebugLog(QString("坏卡计费成功,转入支付"));
    Ptr_ETCCtrl->ChangeToVehMoneyState();
    return true;
}

bool CLaneState_VehInputExit::DoNoCard(QString &sErrorMsg)
{
    if (!IfInputVehInfoFinished()) {
        sErrorMsg = "请输入车辆信息";
        return false;
    }

    if (!Ptr_ETCCtrl->bHasWaitingTransVeh(DevIndex_Manual)) {
        sErrorMsg = "车道队列内无未交易车辆";
        return false;
    }
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
    if (!pCurTransInfo) {
        sErrorMsg = "车道队列内无未交易车辆";
        return false;
    }
    if (pCurTransInfo->hasWriteCard_Exit()) {
        QString sCardId;
        if (pCurTransInfo->mediaType == MediaType_OBU)
            sCardId = QString::fromAscii(pCurTransInfo->IccInfo.ProCardBasicInfo.szCardNo);
        else if (pCurTransInfo->mediaType == MediaType_CPC)
            sCardId = QString::fromAscii(pCurTransInfo->cpcIccInfo.cpcBasicInfo.sCardID);
        sErrorMsg = QString("当前车辆已刷卡,卡号[%1]").arg(sCardId);
        return false;
    }

    //填充车辆信息
    CVehInfo vehInfo;
    CVehAxisInfo vehAxisInfo;
    QString sFDMsg;
    bool bRlt = GetVehInfoForCPC_New(true, pCurTransInfo, vehInfo, vehAxisInfo, sErrorMsg, sFDMsg);
    if (!bRlt) {
        return false;
    }

    QString sVLp = GB2312toUnicode(pCurTransInfo->VehInfo.szVehPlate);
    int nPlateColor = pCurTransInfo->VehInfo.nVehPlateColor;

    CTransInfo transInfo;
    bool bHaveTrans = Ptr_ETCCtrl->CheckVehhasTransByVLP(sVLp, transInfo);
    if (bHaveTrans) {
        if (transInfo.bTransOk()) {
            sErrorMsg = QString("%1本车道已交易,%2分钟之内不允许进行二次交易")
                    .arg(sVLp)
                    .arg(Ptr_Info->GetMaxInterval() / 60);
            DebugLog(sErrorMsg);
            return false;
        }
    }

    int nErrorCode = 0;
    int nRlt = CheckVehList(DevIndex_Manual, sVLp, nPlateColor, nErrorCode, sErrorMsg);
    if (nRlt > 0) {
        if (!CMessageBox::Information_Help(QString("黑名单车辆"), sErrorMsg,
                                           QString("<确定>键继续,<取消>键返回"))) {
            sFDMsg = QString("黑名单车辆");
            return false;
        }
        if (3 == nRlt) {
            nRlt = CFuncMenu::ShowMenu_PrePayBList();
            if (0 == nRlt) return false;
            pCurTransInfo->m_nPrepayList = nRlt;
        }
    }

    QDateTime transTime = QDateTime::currentDateTime();
    //根据卡号，查询入口记录
    CEntryQryCondition condition;
    condition.mediaType = MediaType_None;
    condition.mediaNo = QString("030");
    condition.vehicleId = QString("%1_%2")
            .arg(GB2312toUnicode(pCurTransInfo->VehInfo.szVehPlate))
            .arg(pCurTransInfo->VehInfo.nVehPlateColor);
    QDateTime beginTime = transTime.addDays(-5);
    QDateTime endTime = transTime;
    condition.enStartTime = beginTime.toString("yyyy-MM-ddThh:mm:ss");
    condition.enEndTime = endTime.toString("yyyy-MM-ddThh:mm:ss");
    condition.exStationId = Ptr_Info->GetGBStationId();
    condition.exTollLaneId = Ptr_Info->GetGBLaneId();

    QList<CEntryQryResult> entryList;
    bool bQryResult = CFareCalcUnit::QryEntry_OnLine(condition, entryList);
    bool bQryEntry = false;
    CEntryQryResult qryEntry;
    if (bQryResult && entryList.size() > 0) {
        //查询出的结果中，用此入口信息去做在线计费
        CEntryQryResult *pResult = CFuncMenu::ShowMenu_qryEntryStation(entryList);
        if (pResult) {
            qryEntry = *pResult;
            bQryEntry = true;
        }
    }

    if (bQryEntry) {
        if (!fillEntryInfoByQry(pCurTransInfo, qryEntry, transTime, sErrorMsg)) {
            ShowErrorMessage(sErrorMsg);
            return false;
        }
    } else {
        //未找到入口信息，选站后，使用最小费率计费
        FormSelectStation dlgStation(GetMainDlg());
        COrgBasicInfo stationInfo, laneInfo;
        if (!dlgStation.SelectStation(stationInfo, laneInfo)) {
            return false;
        }
        //通过选择，填充入口信息
        fillEntryInfoBySelStation(pCurTransInfo, stationInfo, laneInfo, transTime);
    }

    bool bAlarm = false;
    bool bLocalEntry = pCurTransInfo->vehEntryInfo.sEnNetWorkIdHex == QString("3601");
    CFeeClass feeClass = bLocalEntry ? FeeClass_ProvCenter : FeeClass_MinistryCenter;
    pCurTransInfo->mediaType = MediaType_None;
    int nDiscountType = 0;
    if (!CalculateTollFee(sErrorMsg, sFDMsg, bAlarm, feeClass, nDiscountType, transTime)) {
        pCurTransInfo->ClearTransInfo();
        return false;
    }
    pCurTransInfo->m_bNoCard = true;
    DebugLog(QString("无卡计费成功,转入支付"));
    Ptr_ETCCtrl->ChangeToVehMoneyState();
    return true;
}

bool CLaneState_VehInputExit::CheckCPCRoadInfo(const CCPCRoadInfo_New &cpcRoadInfo,
                                               const CCPCTollCellInfo_New &cpcTollCellInfo,
                                               bool bIsJc)
{
    if (0 == cpcRoadInfo.nProvCnt) {
        return false;
    }

    if (0 == cpcRoadInfo.nProvFlagCnt || 0 == cpcRoadInfo.bPassFlagCnt) {
        DebugLog(QString("cpc 卡内门架数%1本省门架数%2,路径信息不全")
                 .arg(cpcRoadInfo.bPassFlagCnt)
                 .arg(cpcRoadInfo.nProvFlagCnt));
        return false;
    }
    if (bIsJc) return true;

    if (Ptr_Info->bNewUJ()) {
        int nMinFlagCnt = Ptr_Info->GetMinFlagCnt();
        if (nMinFlagCnt > cpcRoadInfo.bPassFlagCnt) {
            DebugLog(QString("cpc 卡内门架数%1,允许最小门架数:%2,路径信息不全")
                     .arg(cpcRoadInfo.bPassFlagCnt)
                     .arg(nMinFlagCnt));
            return false;
        }
    }

    TollDoorFrameTable *pTable = (TollDoorFrameTable *)CParamFileMgr::GetParamFile(cfDoorCode);
    if (!pTable) {
        ErrorLog("TollDoorFrameTable is null");
        return false;
    }

    TollDoorFrame doorFrame;
    bool bRlt = pTable->QueryGantryInfoByHex(cpcRoadInfo.sNewFlag, doorFrame);
    if (!bRlt) {
        DebugLog(QString("卡内本省最新门架%1非本省门架,路径信息不全").arg(cpcRoadInfo.sNewFlag));
        return false;
    }

    QString sError;
    if (!cpcTollCellInfo.CheckTollInfo(sError)) {
        DebugLog(sError);
        return false;
    }
    return true;
}

bool CLaneState_VehInputExit::DoPaperCard(QString &sErrorMsg)
{
    if (!IfInputVehInfoFinished()) {
        sErrorMsg = "请输入车辆信息";
        return false;
    }

    if (!Ptr_ETCCtrl->bHasWaitingTransVeh(DevIndex_Manual)) {
        sErrorMsg = "车道队列内无未交易车辆";
        return false;
    }
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
    if (!pCurTransInfo) {
        sErrorMsg = "车道队列内无未交易车辆";
        return false;
    }
    if (pCurTransInfo->hasWriteCard_Exit()) {
        sErrorMsg = "当前有已刷卡未完成业务";
        return false;
    }

    //填充车辆信息
    CVehInfo vehInfo;
    CVehAxisInfo vehAxisInfo;
    QString sFDMsg;
    bool bAlarm = false;

    bool bRlt = GetVehInfoForCPC_New(true, pCurTransInfo, vehInfo, vehAxisInfo, sErrorMsg, sFDMsg);
    if (!bRlt) {
        return false;
    }

    QString sVLp = GB2312toUnicode(pCurTransInfo->VehInfo.szVehPlate);
    int nPlateColor = pCurTransInfo->VehInfo.nVehPlateColor;

    /*
    bool bHaveTrans = Ptr_ETCCtrl->CheckVehhasTransByVLP(sVLp, transInfo);
    if (bHaveTrans) {
        if (transInfo.bTransOk()) {
            sError = QString("%1本车道已交易,%2分钟之内不允许进行二次交易")
                         .arg(sVLp)
                         .arg(Ptr_Info->GetMaxInterval() / 60);
            DebugLog(sErrorMsg);
            return false;
        }
    }*/

    int nErrorCode = 0;
    int nRlt = CheckVehList(DevIndex_Manual, sVLp, nPlateColor, nErrorCode, sErrorMsg);
    if (nRlt > 0) {
        if (!CMessageBox::Information_Help(QString("黑名单车辆"), sErrorMsg,
                                           QString("<确定>键继续,<取消>键返回"), false)) {
            bAlarm = true;
            sFDMsg = QString("黑名单车辆");
            return false;
        }
        if (3 == nRlt) {
            nRlt = CFuncMenu::ShowMenu_PrePayBList();
            if (0 == nRlt) return false;
            pCurTransInfo->m_nPrepayList = nRlt;
        }
    }

    //输入纸卡卡号
    FormInputCardNo dlgInputCardNo(GetMainDlg());
    if (!dlgInputCardNo.InputCardNo(FormInputCardNo::CardNoType_Paper, 14)) {
        return false;
    }
    QString sCardNo = dlgInputCardNo.GetInputResult();

    //输入站代码
    COrgBasicInfo stationInfo;
    FormInputEnStationID dlgInputStationID(GetMainDlg());
    if (!dlgInputStationID.InputEnStation(stationInfo)) {
        return false;
    }

    CTransInfo transInfo;
    bool bHasTrans = Ptr_ETCCtrl->CheckVehhastransForPaperCard(sCardNo, sVLp, transInfo, sErrorMsg);
    if (bHasTrans) {
        return false;
    }

    QDateTime transTime = QDateTime::currentDateTime();
    //通过选择，填充入口信息
    COrgBasicInfo laneInfo;
    fillEntryInfoBySelStation(pCurTransInfo, stationInfo, laneInfo, transTime);

    //填充纸卡卡号
    pCurTransInfo->mediaType = MediaType_Paper;
    pCurTransInfo->m_sPaperId = sCardNo;

    int nDiscountType = 0;
    if (!CalculateTollFee(sErrorMsg, sFDMsg, bAlarm, FeeClass_Min, nDiscountType, transTime)) {
        GetMainDlg()->ShowPromptMsg(sErrorMsg, true);
        pCurTransInfo->ClearTransInfo();
        return false;
    }
    DebugLog(QString("纸卡计费成功,转入支付"));
    Ptr_ETCCtrl->ChangeToVehMoneyState();

    return true;
}

void CLaneState_VehInputExit::ShowHistoryQRCode()
{
    QStringList lstData = FormQRCode::LoadHistoryQrCodeInfo();
    QList<CListData> lstMenuItem;
    for (int i = 0; i < lstData.size(); i++) {
        bool bEnable = (i == 0);
        QString sItem = lstData.at(i);
        QString sVehId = sItem.left(sItem.indexOf(","));
        sVehId = sVehId.left(sVehId.indexOf("_"));
        QString sVlpColor = sVehId.mid(sVehId.indexOf("_") + 1);

        QString exTimeStr = sItem.mid(sItem.indexOf(",") + 1);
        QDateTime exTime = QDateTime::fromString(exTimeStr, "yyyy-MM-ddThh:mm:ss");

        QString sShowItem = QString("%1  (%2)").arg(sVehId).arg(exTime.toString("MM-dd hh:mm:ss"));

        lstMenuItem.push_back(CListData(i, sShowItem, sShowItem, NULL, true, bEnable));
    }
    CListDlg dlg("显示绿通车二维码", "请按【数字】键选择", true, true, GetMainDlg());
    dlg.ShowList(lstMenuItem, 0);
    int nResult = dlg.doModalShow();
    if (nResult > 0 && nResult <= lstData.size()) {
        QString sItem = lstData.at(nResult - 1);
        QString sVehId = sItem.left(sItem.indexOf(","));
        QString exTimeStr = sItem.mid(sItem.indexOf(",") + 1);
        QDateTime exTime = QDateTime::fromString(exTimeStr, "yyyy-MM-ddThh:mm:ss");
        GetMainDlg()->ShowHistoryQrCodeImage(sVehId, exTime);
    }
}

bool CLaneState_VehInputExit::CheckMaxFee(int nVehClass, int nVehType, quint32 fee)
{
    quint32 nMaxFee = 0;
    if (nVehClass == VC_Car1)
        nMaxFee = 10000;
    else
        nMaxFee = 20000;

    if (nVehType == UVT_BigTruck) nMaxFee = 50000;

    return fee <= nMaxFee * 100;
}

bool CLaneState_VehInputExit::ProcessKeyReprint()
{
    CTransInfo *pLastTransInfo = Ptr_ETCCtrl->RemoveTransInfoFromQue(true, false);
    if (!pLastTransInfo) {
        ShowErrorMessage("无已交费车辆,废票键无效");
        return false;
    }
    if (Ptr_Info->bEticket()) {
        if (!pLastTransInfo->bTransOk() || pLastTransInfo->m_payType != PayType_Cash) {
            ShowErrorMessage("车辆未打票,废票键无效");
            return false;
        }
    } else {
        if (!pLastTransInfo->bTransOk() || 0 == pLastTransInfo->m_llInvoiceId) {
            ShowErrorMessage("车辆未打票,废票键无效");
            return false;
        }
    }

    QString sOpStateName;
    if (!CheckAndSetOpState(opState_Reverse, sOpStateName)) {
        ShowErrorMessage(QString("%1,废票键无效").arg(sOpStateName));
        return false;
    }

    DebugLog("出口废票处理");
    // FormReverse frmReverse;
    int nRlt = 0;
    if (!Ptr_Info->bEticket()) {
        nRlt = CFuncMenu::ShowMenu_Reprint();
        if (0 == nRlt) {
            SetOpState(opState_None);
            return true;
        }
    }

    CPrintContent printContent;
    printContent.sEntryStation = pLastTransInfo->vehEntryInfo.sEnStaionName;
    printContent.sExitStation = Ptr_Info->GetStationName();
    printContent.nVehClass = pLastTransInfo->VehInfo.VehClass;
    printContent.nMoney = pLastTransInfo->m_nTransFee;
    if (Ptr_Info->bEticket()) {
        printContent.sVehPlate = GB2312toUnicode(pLastTransInfo->VehInfo.szVehPlate);
        printContent.bPlateColor = pLastTransInfo->VehInfo.nVehPlateColor;
        printContent.sListNo = pLastTransInfo->m_sListNo;
        printContent.sQrCode = pLastTransInfo->m_sQrCode;
    } else {
        if (1 == nRlt)
            printContent.nTicketNumber = pLastTransInfo->m_llInvoiceId;
        else
            printContent.nTicketNumber = Ptr_ETCCtrl->GetInvoiceInfo()->GetCurrentCode();
    }

    printContent.sOprID = QString("%1").arg(Ptr_ETCCtrl->GetOperInfo().dwOper);
    printContent.t = pLastTransInfo->TransTime;
    printContent.bFareByWeight = false;
    printContent.nAxis = pLastTransInfo->vehEntryInfo.VehicalAxles;
    printContent.nTotalWeight = pLastTransInfo->vehEntryInfo.dwTotalWeight;
    printContent.nOverWeight = 0;
    // printContent.sDeputyInfo
    CPrinterDevice *pPrinter = CDeviceFactory::GetPrinterDev();
    pPrinter->SetPrintContent(printContent);
    if ((!Ptr_Info->bEticket()) && (2 == nRlt)) {
        Ptr_ETCCtrl->GetInvoiceInfo()->ReduceOne();
        //发送原流水
        CTransInfo lastTransInfo = *pLastTransInfo;
        lastTransInfo.m_bReprint = true;
        if (!Ptr_ETCCtrl->SaveLastTransInfo(0, &lastTransInfo, true, false, false, false)) {
        }
        //发送冲减流水
        lastTransInfo.m_bReprint = false;
        QString sId = lastTransInfo.m_sId;
        lastTransInfo.m_sId.clear();  //必须清除该id，否则冲减流水就会用该id号，导致重复
        Ptr_ETCCtrl->saveHedgeWaste_Ex(&lastTransInfo, sId);
        pLastTransInfo->m_llInvoiceId = printContent.nTicketNumber;
        pLastTransInfo->m_sId.clear();  //必须清
    }

    bool bRlt = pPrinter->Print(Ptr_Info->bEticket());
    if (!bRlt) {
        if (Ptr_Info->bEticket()) {
            ShowErrorMessage(QString("重打失败,凭证号:%1").arg(pLastTransInfo->m_sListNo));
        } else {
            ShowErrorMessage(QString("重打失败,票号:%1").arg(pLastTransInfo->m_llInvoiceId));
            DebugLog(QString("出口重打失败,票号:%1").arg(pLastTransInfo->m_llInvoiceId));
        }
    }
    if (Ptr_Info->bHaveCardMgr()) {
        SleeperThread::msleep(2000);
        // CDeviceFactory::GetPayMentMgr()->DeliverTicket(pLastTransInfo->m_bCardMgrIndex);
    }

    SetOpState(opState_None);
    return bRlt;
}

bool CLaneState_VehInputExit::CheckOutTime(CTransInfo *pTransInfo, QString &sError, int nMaxTime)
{
    if (!pTransInfo) {
        return false;
    }

    QDateTime curTime = QDateTime::currentDateTime();
    if (pTransInfo->vehEntryInfo.nEntryType == Entry_ByCard ||
            pTransInfo->vehEntryInfo.nEntryType == Entry_ByQry) {
        if (pTransInfo->vehEntryInfo.nPassTime > 60 * 60 * 24 * 7) {
            sError = QString("行驶超过7天,入口时间:%1")
                    .arg(pTransInfo->vehEntryInfo.EnTime.toString("yyyy-MM-dd hh:mm:ss"));
            return true;
        }
    }

    if (pTransInfo->vehEntryInfo.nEntryType == Entry_ByCard &&
            pTransInfo->mediaType == MediaType_CPC) {
        if (FeeClass_Card == pTransInfo->actualFeeClass) {
            if (0 == pTransInfo->m_nTransFee) {
                DebugLog(QString("通行费为0,不比较超时"));
                return false;
            }
            qint32 nTotalMiles = 0, nTotalFee = 0,
                    nPayFee = 0;  // = pTransInfo->cpcIccInfo.cpcRoadInfo.nProvTradeMeter_After;

            pTransInfo->cpcIccInfo.cpcTollCellInfo_After.GetTotalFee(nTotalMiles, nTotalFee,
                                                                     nPayFee);

            int nSpeed = 60000 / 60;                         //单位米/分钟
            quint32 nTotalTime = nTotalMiles / nSpeed * 60;  //行驶时间单位秒
            QDateTime enTime;
            enTime = pTransInfo->vehEntryInfo.EnTime;

            /*
                        if (pTransInfo->vehEntryInfo.sEnNetWorkIdHex == QString("3601") &&
                            1 == pTransInfo->cpcIccInfo.cpcRoadInfo.nProvCnt)
                            enTime = pTransInfo->vehEntryInfo.EnTime;
                        else
                            enTime =
                                QDateTime::fromTime_t(pTransInfo->cpcIccInfo.cpcRoadInfo.nProvEnFlagPassTime);

                                */

            DebugLog(QString("检查车辆超时,计费里程:%1,入口时间:%2")
                     .arg(nTotalMiles)
                     .arg(enTime.toString("yyyy-MM-dd hh:mm:ss")));

            if (enTime < curTime) {
                qint32 nFactTime = enTime.secsTo(curTime);
                if (nFactTime > nTotalTime) {
                    quint32 nOutTime = nFactTime - nTotalTime;
                    if (nOutTime * 10 > nTotalTime) {
                        pTransInfo->m_nOutTimes = nOutTime;
                    } else {
                        DebugLog(QString("超时时间%1，小于总时间%2 的百分之十")
                                 .arg(nOutTime)
                                 .arg(nTotalTime));
                        pTransInfo->m_nOutTimes = 0;
                        return false;
                    }
                    DebugLog(QString("车辆超时%1秒").arg(pTransInfo->m_nOutTimes));

                    if (pTransInfo->m_nOutTimes < nMaxTime * 60) {
                        return false;
                    }
                    int nHours = pTransInfo->m_nOutTimes / 3600;
                    int nMinutes = (pTransInfo->m_nOutTimes % 3600) / 60;

                    QString sEnTime =
                            pTransInfo->vehEntryInfo.EnTime.toString("yyyy-MM-dd hh:mm:ss");
                    if (nHours > 0)
                        sError = QString("车辆超时%1小时%2分钟 入口站:%3 入口时间:%4")
                                .arg(nHours)
                                .arg(nMinutes)
                                .arg(pTransInfo->vehEntryInfo.sEnStaionName)
                                .arg(sEnTime);
                    else
                        sError = QString("车辆超时%1分钟").arg(nMinutes);
                    return true;
                }
            }
        } else if (FeeClass_Min == pTransInfo->actualFeeClass) {
        }
    }
    return false;
}

bool CLaneState_VehInputExit::ReGetTac(CCardReader *pCardReader, CTransInfo *pcurTransInfo,
                                       QString &sError, QString &sFDMsg)
{
    quint16 wCardSeq;
    quint8 bKeyVer = 0, bKeyType = 0;
    bool getRlt = pCardReader->ProGetCardSeq(wCardSeq, bKeyVer, bKeyType,
                                             pcurTransInfo->IccInfo.ProCardBasicInfo.bSM4);
    if (!getRlt) {
        sError = QString("重取卡片交易序列号失败");
        sFDMsg = QString("读卡失败");
        return false;
    } else {
        DebugLog(QString("重取tac,卡片交易序列号:%1,keyver:%2,keytype:%3")
                 .arg(wCardSeq)
                 .arg(bKeyVer)
                 .arg(bKeyType));
    }
    quint32 nTac = 0;
    getRlt = pCardReader->GetLastTac(wCardSeq, nTac);
    if (!getRlt) {
        sError = QString("重取tac 失败");
        return false;
    }

    CProCardConsumeInfo consumeInfo;
    QString sConsumeTime =
            pcurTransInfo->IccInfo.CardTollInfo.szPassTime.toString("yyyyMMddhhmmss");
    qsnprintf(consumeInfo.szConsumeTime, sizeof consumeInfo.szConsumeTime, "%s",
              sConsumeTime.toAscii().data());
    consumeInfo.dwMoney = 0;
    consumeInfo.dwTermSeq = 0;
    consumeInfo.dwTac = nTac;
    quint32 dwBigTac = qToBigEndian(consumeInfo.dwTac);
    memcpy(consumeInfo.bTac, &dwBigTac, 4);
    consumeInfo.wCardSeq = wCardSeq;
    consumeInfo.dwBalanceBefore = pcurTransInfo->IccInfo.dwBalance;
    consumeInfo.dwBalanceAfter = pcurTransInfo->IccInfo.dwBalance;
    consumeInfo.bKeyVer = bKeyVer;   // 1;
    consumeInfo.KeyType = bKeyType;  // 0;
    // pcurTransInfo->SetConsumeInfo(consumeInfo);
    pcurTransInfo->SetPayCardInfo(pcurTransInfo->IccInfo.ProCardBasicInfo, consumeInfo);
    CTransInfo *pLastTransInfo = Ptr_ETCCtrl->GetLastTransInfo(DevIndex_Manual);
    pcurTransInfo->SaveTo(*pLastTransInfo);
    pLastTransInfo->CompleteTrans(DevIndex_Manual, TransPT_ETCCard, NULL, Tr_Successed,
                                  CTransInfo::Ts_WaitToSave);
    pLastTransInfo->etcTransType = TransType_ExOnlyAppTrans;
    pLastTransInfo->m_bForTac = true;
    Ptr_ETCCtrl->CompleteTrans(Tr_Successed, DevIndex_Manual, true);
    
    // 交易成功后停止定时器
    if (m_timerCardMgr.isActive()) {
        m_timerCardMgr.stop();
        DebugLog("交易成功，停止出口车道卡机定时器");
    }
    pcurTransInfo->ClearTransInfo();
    CAbstractState::ClearVehInfo();

    return true;
}

bool CLaneState_VehInputExit::CheckEmVehList(CTransInfo *pcurTransInfo, int &nDiscountType)
{
    bool bEmVeh = false;
    CEmVehList *pEmVehList = (CEmVehList *)CParamFileMgr::GetParamFile(cfEmVeh);
    CEmVehInfo emVehInfo;
    nDiscountType = 0;
    if ((!bEmVeh) && pEmVehList) {
        QString sCardId;
        QString sPlate = GB2312toUnicode(pcurTransInfo->VehInfo.szVehPlate);
        bEmVeh =
                pEmVehList->IsEmVeh(emVehInfo, nDiscountType, sCardId, sPlate,
                                    pcurTransInfo->VehInfo.nVehPlateColor, CEmVehList::QryType_VLP);
        if (bEmVeh) {
            DebugEmvehInfo(emVehInfo);
            CUnionVehType gbVehType = UVT_Normal;
            QString sEmVehName = QString("优免车");
            if (emVehInfo.vehicleSign == "0x05") {
                sEmVehName = QString("疫苗车");
                gbVehType = UVT_Vaccin;
            } else if (emVehInfo.vehicleSign == "0x06") {
                sEmVehName = QString("应急抢险车");
                gbVehType = UVT_EmergencyRescue;
            }
            if (gbVehType != UVT_Normal) {
                if (1) {  // (2 == emVehInfo.checkType) {
                    QString sMsg = QString("请核验%1").arg(sEmVehName);
                    bool bRlt = ShowInformation_Help(
                                QString("优免车核验"), sMsg, "按【确定】键为优免车,【取消】键不为优免车",
                                true, QString("车辆信息核验"), CSpEventMgr::SpEvent_Other, true);
                    if (bRlt) {
                        pcurTransInfo->SetEmVehInfo(bEmVeh, emVehInfo);
                        pcurTransInfo->SetGBVehType(gbVehType);
                        GetMainDlg()->ShowLog(QString("名单优免车:%1").arg(sEmVehName));
                    } else {
                        bEmVeh = false;
                    }
                } else {
                    pcurTransInfo->SetEmVehInfo(bEmVeh, emVehInfo);
                    pcurTransInfo->SetGBVehType(gbVehType);
                }
            } else {
                bEmVeh = false;
            }
        }
    }
    return bEmVeh;
}

bool CLaneState_VehInputExit::QryOrSelectEntryInfo(const QDateTime &transTime,
                                                   CTransInfo *pCurTransInfo)
{
    QString sTitle = QString("车辆行驶超过%1天").arg(Ptr_Info->GetMaxDriveDays());
    if (!ShowInformation_Help(sTitle, "在线查询入口站?", "按【确定】键开始,【取消】键返回", false,
                              "")) {
        return false;
    }
    //根据卡号，查询入口记录
    CEntryQryCondition condition;
    condition.mediaType = MediaType_None;
    condition.mediaNo = QString("030");
    condition.vehicleId = QString("%1_%2")
            .arg(GB2312toUnicode(pCurTransInfo->VehInfo.szVehPlate))
            .arg(pCurTransInfo->VehInfo.nVehPlateColor);
    QDateTime beginTime = transTime.addDays(0 - Ptr_Info->GetMaxDriveDays());
    QDateTime endTime = transTime;
    condition.enStartTime = beginTime.toString("yyyy-MM-ddThh:mm:ss");
    condition.enEndTime = endTime.toString("yyyy-MM-ddThh:mm:ss");

    QList<CEntryQryResult> entryList;
    bool bQryResult = CFareCalcUnit::QryEntry_OnLine(condition, entryList);
    bool bQryEntry = false;
    CEntryQryResult qryEntry;
    if (bQryResult && entryList.size() > 0) {
        //查询出的结果中，用此入口信息去做在线计费
        // CEntryQryResult *pResult =CFuncMenu::ShowMenu_qryEntryStation(entryList);
        qryEntry = entryList.last();
        bQryEntry = true;
    }

    QString sErrorMsg;
    if (bQryEntry) {
        if (fillEntryInfoByQry(pCurTransInfo, qryEntry, transTime, sErrorMsg)) {
            // ShowErrorMessage(sErrorMsg);
            return true;
        } else {
            DebugLog(sErrorMsg);
        }
    }
    //未找到入口信息，选站后，使用最小费率计费
    FormSelectStation dlgStation(GetMainDlg());
    COrgBasicInfo stationInfo, laneInfo;
    if (!dlgStation.SelectStation(stationInfo, laneInfo)) {
        return false;
    }
    //通过选择，填充入口信息
    fillEntryInfoBySelStation(pCurTransInfo, stationInfo, laneInfo, transTime);
    return true;
}

bool CLaneState_VehInputExit::CompareVehPlateAndResetVehInfo(CTransInfo *pTransInfo,
                                                             QString &sCompareTitle,
                                                             QString &sCompareDisp)
{
    QString sEnPlate = GB2312toUnicode(pTransInfo->vehEntryInfo.szEnVLP);
    QString sEnFullPlate =
            QString("%1%2").arg(GetVehPlateColorName(pTransInfo->vehEntryInfo.bEnVLPC)).arg(sEnPlate);

    // GetFullVehPlate(pTransInfo->vehEntryInfo.szEnVLP, pTransInfo->vehEntryInfo.bEnVLPC,
    // sEnPlate);

    QString sFullPlate;
    GetFullVehPlate(pTransInfo->VehInfo.szVehPlate, pTransInfo->VehInfo.nVehPlateColor, sFullPlate);
    if (sEnFullPlate != sFullPlate) {
        DebugLog(QString("入%1,出%2,车牌不符").arg(sEnFullPlate).arg(sFullPlate));

        if (pTransInfo->VehInfo.nVehClassWay != VehClassway_Input) {  //不是人工输入
            VCRDev *pDev = CDeviceFactory::GetVCRDev();
            if (pDev) {
                //以入口车牌查询车型结果
                VcrResult vcrResult;
                bool bResult =
                        pDev->GetVcrResult(pTransInfo->vehEntryInfo.bEnVLPC, sEnPlate, vcrResult);
                if (!bResult) {
                    /*避免重复交易除掉取上一辆车的处理
                    bResult = pDev->GetPreVcrResult(pTransInfo->vehEntryInfo.bEnVLPC, sEnPlate,
                                                    vcrResult);
                    if (bResult)
                        DebugLog(QString("根据入口车牌号查询小黄人上一辆车返回结果,vehclass:%1,"
                                         "vehplate:%2,carid:%3")
                                     .arg(vcrResult.vehclass)
                                     .arg(vcrResult.sPlate)
                                     .arg(vcrResult.dwCarID));
                                     */

                } else {
                    DebugLog(QString("根据入口车牌号%1查询小黄人返回结果,vehclass:%2,vehplate:%3,"
                                     "carid:%4,车牌有效:%5,color:%6")
                             .arg(sEnPlate)
                             .arg(vcrResult.vehclass)
                             .arg(vcrResult.sPlate)
                             .arg(vcrResult.dwCarID)
                             .arg(vcrResult.bPlateValid)
                             .arg(vcrResult.nColor));
                    if (0 == vcrResult.bPlateValid ||
                            vcrResult.nColor != pTransInfo->vehEntryInfo.bEnVLPC) {
                        bResult = false;
                    }
                }
                if (bResult && (pTransInfo->vehEntryInfo.bEnVC == vcrResult.vehclass)) {
                    //以新的结果更新原来结果
                    pTransInfo->VehInfo.AutoVehClass = vcrResult.vehclass;
                    pTransInfo->VehInfo.VehClass = vcrResult.vehclass;
                    qsnprintf(pTransInfo->VehInfo.szAutoVehPlate,
                              sizeof pTransInfo->VehInfo.szAutoVehPlate, "%s",
                              pTransInfo->vehEntryInfo.szEnVLP);
                    qsnprintf(pTransInfo->VehInfo.szVehPlate, sizeof pTransInfo->VehInfo.szVehPlate,
                              "%s", pTransInfo->vehEntryInfo.szEnVLP);
                    pTransInfo->VehInfo.nVehPlateColor = pTransInfo->vehEntryInfo.bEnVLPC;
                    pTransInfo->VehInfo.nAutoVehPlateColor = pTransInfo->vehEntryInfo.bEnVLPC;
                    pTransInfo->VehInfo.nVehClassWay = VehClassWay_Auto;
                    pTransInfo->m_vcrResult = vcrResult;
                    DebugLog(QString("以入口信息更新出口,vehclass:%1,vehPlate:%2")
                             .arg(vcrResult.vehclass)
                             .arg(sEnPlate));
                    return true;
                } else {
                    DebugLog(QString("车牌不符,根据入口车牌%1查询小黄人无返回").arg(sEnPlate));
                }
            }
        }

        sCompareTitle = "车牌";
        sCompareDisp = QString("入口:%1,出口:%2").arg(sEnFullPlate).arg(sFullPlate);
        DebugLog(QString("入出车牌不符,入:%1出:%2").arg(sEnFullPlate).arg(sFullPlate));

        //如出口车型不符
        sCompareTitle = sCompareTitle + QString("不符");
        return false;

        /*
        if (!ShowInformation_Help(sCompareTitle, QString("%1,是否继续计费?").arg(sCompareDisp),
                                  QString("按<确认>键按出口车牌计费,按<取消>键重新输入"), true,
                                  sCompareTitle, CSpEventMgr::SpEvent_VehPlateDiff, true)) {
            return false;
        }
        DebugLog(QString("入出车辆信息不符,转在线计费"));
        if (feeClass != FeeClass_Min)
            feeClass = bIsLocal ? FeeClass_ProvCenter : FeeClass_MinistryCenter;
            */
    }
    return true;
}

void CLaneState_VehInputExit::OnVehDetectTimer()
{
    bool bMoved = false;

    if (Ptr_Info && Ptr_Info->GetCardMgrType() == 31){
        DebugLog(QString("伸出机械臂"));
        bMoved = true;
    } else {
        // 根据卡头伸缩配置决定逻辑
        if (Ptr_Info->GetCardHeadStretchConfig() == 0) {
            // 配置为0时，只要存在线圈有信号就发送卡头伸出指令
            DebugLog(QString("卡头伸缩配置为0，检测到线圈信号，直接伸出机械臂"));
            bMoved = true;
        } else {
            // 配置非零时，依据现有逻辑判断（车型判断）
            VcrResult vcrResult;
            bool bRlt = CDeviceFactory::GetVCRDev()->GetFistVcrResult(vcrResult);
            if (bRlt) {
                if (vcrResult.vehclass > 0 ) {
                    if ((vcrResult.vehclass == VC_Truck1) || (vcrResult.vehclass < VC_Car3)) {
                        DebugLog(QString("车型识别首辆车车型%1,伸出机械臂").arg(vcrResult.vehclass));
                        bMoved = true;
                    }
                }
            }
        }
    }

    if (bMoved) {
        CDeviceFactory::GetPayMentMgr()->SetVehState(true);
    }
    return;
}

void CLaneState_VehInputExit::OnCardMgrTimer()
{
    //StopCardMgrTimer();
    // 定时器触发的处理逻辑
    if (Ptr_Info && Ptr_Info->GetCardMgrType() == 31) {
        if (m_nTimerCardMgrInterval2Flag) {
            m_nTimerCardMgrInterval2Flag = false;
            m_timerCardMgr.stop();
            DebugLog("天线进入B4帧交易，出口车道卡机定时器延时触发...");
            m_timerCardMgr.start(m_nTimerCardMgrInterval2);
            return;
        }
        // 这里添加对福赛特-中瑞卡机(m_nCardMgrType=31)的定时处理逻辑
        DebugLog("出口车道卡机定时器触发...");
        OnVehDetectTimer();
    }
}

// 响应停止卡机定时器的槽函数
void CLaneState_VehInputExit::OnStopCardMgrTimer()
{
    // 调用公共函数停止定时器
    StopCardMgrTimer();
}
