#ifndef CETCLANECTRL_H
#define CETCLANECTRL_H

#include <QList>
#include <QMutex>
#include <QObject>
#include <QTimer>

#include "batchmgr.h"
#include "invoice.h"
#include "lane.pb.h"
#include "lanestate_motorcade.h"
#include "lanestate_psamauth.h"
#include "lanestate_unlogin.h"
#include "lanestate_vehinput.h"
#include "lanestate_vehmoney.h"
#include "lanestate_waitvehpass.h"
#include "lanestate_etcstate.h"
#include "localdatamgr.h"
#include "qrcode.h"
#include "secureverify.h"
#include "shiftmgr.h"
#include "tollgantrymgr.h"
#include "transinfo.h"
#include "updateserver.h"

class CETCLaneCtrl : public QObject
{
    Q_OBJECT

public:
    enum WASTE_DISPLAYTYPE
    {
        DT_COMPLETE,
        DT_SAVEWASTE,
        DT_BOTH
    };

public:
    CETCLaneCtrl();
    virtual ~CETCLaneCtrl();

    static CETCLaneCtrl *GetETCLaneCtrl()
    {
        if (!m_pETCLaneCtr) m_pETCLaneCtr = new CETCLaneCtrl();
        return m_pETCLaneCtr;
    }
    static void ReleaseStETCLaneCtrl()
    {
        if (m_pETCLaneCtr) {
            delete (m_pETCLaneCtr);
            m_pETCLaneCtr = NULL;
        }
    }

public:
    CShiftMgr *GetShiftMgr() { return &m_ShiftMgr; }
    CLocalDataMgr *GetDataMgr();

    bool InitETCLaneCtrl(int nStationId, int nLaneId, qint32 nLaneType);
    bool InitLocalDB(const QString &sDBPath, const QString &sBakDBPath, bool bRemoveBakFile);
    bool GetAndResendTmpFile();
    bool AppStart(QString &sError);
    void AppClose();
    bool SaveAppStarMsg(bool bStart);

    void Login(const COperInfo &OperInfo, const QDateTime &loginTime);
    void UnLogin(bool bIsReSend = false);
    void AutoLogin();
    void SetLaneStatus(qint32 nLaneStatus);

    void InitAllState();
    void ClearAllState();
    void SetLaneStateUI(int nStateId, CAbstractStateUI *pStateUI);
    //切换状态机
    void ChangeToVehInputState();
    void ChangeToUnLoginState();
    void ChangeToWaitVehPassState();
    void ChangeToMotorcade();
    void ChangeToPSAMAuthState();
    void ChangeToVehMoneyState();

    void StartETCState();
    void CloseETCState();

    void SaveVLPRSumWaste(const QString &sHourBatchNum, quint32 vehicleDataCount,
                          quint32 vehiclePicCount);
    //设置登录操作员信息
    void SetOperInfo(COperInfo OperInfo) { m_OperInfo = OperInfo; }
    //获取登录操作员信息
    COperInfo GetOperInfo() { return m_OperInfo; }
    //获取票据信息
    CInvoiceInfo *GetInvoiceInfo() { return &m_InvoiceInfo; }

    bool SaveWorkSumData(const CShiftSumInfo &ShiftSumInfo);
    // nDataType 0-当前班次 1-上一班次
    bool SaveBigShiftSumData(const CShiftSumInfo &ShiftSumInfo, int nDataType);

    bool EndCurShiftInfo(CShiftSumInfo &lastShiftSumInfo);
    bool SaveGanteyHourSumData(const QByteArray &SumInfo);
    bool SaveShiftSumMsg(const CShiftSumInfo &OldShiftSumInfo,
                         const CShiftSumInfo &NewShiftSumInfo);
    bool SaveLastTransInfo(int nInductCnt, CTransInfo *pTransInfo, bool bGetCapInfo,
                           bool bSaveFailed = false, bool bClearCap = true,
                           bool bSendGantry = true);

    //判断根据obu 车牌、卡号判断车辆是否已经交易了
    bool CheckVehhasTransByOBUId(const quint32 dwOBUId, int nSeconds, CTransInfo &transInfo);
    bool CheckVehhasTransByVLP(const QString &sVLP, CTransInfo &transInfo);
    bool CheckVehhasTransByCardId(const QString &sCardId, CTransInfo &transInfo);

    bool CheckVehhastransForPaperCard(const QString &sPaperNo, const QString &sPlate,
                                      CTransInfo &transInfo, QString &sError);

    //从已经交易队列内删除一辆车，参数为空时，删除首辆车，主要是入口发卡完成后，倒车业务
    bool RemoveHasTransInfo(const CTransInfo *pTransInfo = NULL);

    //判断是否为倒车车辆，如果是则返回该倒车信息。
    bool CheckReservedVeh(quint32 dwOBUId);
    //判断车辆是否在当前车道等待离开队列内（含前天线交易完没进入车道）。一般用来判断重复交易车辆，是否还在车道内
    bool CheckVehInVehQue(const CTransInfo *pTransInfo);

    //检查车牌是否在车型队列中且不在队首位置
    bool CheckVehPlateInQueueNotFirst(const QString &sVehPlate, QString &sError);

    bool IsBarUp() { return m_bBarState; }

    void ShowQrCode(const CTransInfo *pTransInfo);
    void TestQrCode();

    void SetFrontRsuState(bool bWork);
    bool IsFrontRsuWork();

public:
    const quint8 *GetTermId() { return m_TermCode; }
    void SetTermId(const quint8 *pTermId, uint nMaxLen);
    void SetAuthorizeInfo(const CAuthorizeInfo &AuthInfo);

public:
    bool OnDIChangeEvent(qint32 nDI, bool bStatus, QString sStatus);
    bool OnDOChangeEvent(qint32 nDo, bool bStatus);
    void OnDevStatusChangedEvent(qint32 nDevId, qint32 bStatus);
    bool ProcessBackLoopEvent(bool bStatus);
    bool ProcessDetectLoopEvent(bool bStatus);
    bool ProcessFrontLoopEvent(bool bStatus);

    void ProcessFrontLoopOnVehEvent();

    bool SaveVehCapPic(QString &sRelativeFileName);
    bool SaveVLPImg(const CVPRResult &vehinfo, QString &sBigImgFileName,
                    QString &sRelativeFileName);
    bool SetETCAutoVehPlate(int nIndex, const CVehInfo &vehInfo, const QString &sBigImgFileName,
                            const QString &sRelativeFileName, CAutoRegInfo &autoRegInfo,
                            VcrResult *pVcrResult = NULL);

    bool SaveAutoVehPlateMsg(const CVehInfo &vehInfo, VcrResult *pVcrResult,
                             CAutoRegInfo &autoRegInfo);

    bool SaveVIPUMsg(const CAutoRegInfo &autoRegInfo, VcrResult *pResult);
    bool SaveVIPUMsg_VcrResult(const QString &sId, VcrResult *pVcrResult);

    bool GetETCAutoRegInfo(int nIndex, CAutoRegInfo &AutoRegInfo, bool bClearRlt = true,
                           bool bCheckTime = true);

    bool GetETCAutoRegInfoByPlate(const QString &sPlate, CAutoRegInfo &autoRegInfo);
    bool bReETCAutoCap(int nIndex, const QString &sAutoVehPlate);
    void ClearAutoRegInfo(int nIndex);

    //取加入到队列，但是还没保存的车辆
    CTransInfo *GetLastWasteOfNotSaved(bool bTransOk);

    //增加新的过车信息进入交易完队列
    CTransInfo *AddNewTransInfoToFrontList(const CTransInfo &TransInfo, bool bReserved = false);
    //
    CTransInfo *PopTransInfoFromFrontList();

    void AddToReserveQue(const CTransInfo *pTransInfo);

    //增加一辆车进入过车队列，缺省从队列后增加
    void AddTransVehToList(int nInductCnt, CTransInfo *pTransInfo, bool AddToFront = false);
    //将前天线交易完成的车辆更新到后面过车队列里
    void AddTransVehToListFromFrontQueue(int nInductCnt, CTransInfo *pTransInfo);

    //判断过车队列里有异常车，前天线交易时判断
    bool bHaseAbnormalVehInQueue(QString &sError, quint32 &dwOBUID);
    //判断过车队列内第一辆车是异常车
    bool bAbnormalVehOfTheFirstVehInQueue(CTransInfo &transInfo);

    //车道内有干扰车辆
    int HaveDisturbCar(int nDevIndex, quint32 dwOBUId, const CVehPosInfo *pVehPos, QString &sError);

    //前天线交易成功尚未进入车道内的车辆数
    int GetVehCount_FrontQue();

    //队列内交易完车辆数（前后队列车辆和）
    int GetAllVehCountInQue();
    //后队列车辆数
    int GetVehCount_BackQue();

    void SetVehLeadOut();
    bool SetAllowPass(bool bForceUp = false, bool bOutTime = true);
    void SetRefusePass();
    //后线圈过车
    void VehPass();

    void SimulateDownBar();

    void DoLongVeh();
    bool bLongVeh() { return m_bLongVeh; }

    //取当前队列内第一辆车，bFront=true队列第一辆车，false -队列最后一辆车 bDelte true从队列内删除，
    CTransInfo *RemoveTransInfoFromQue(bool bFront, bool bDelete = true);
    //更改队列内车辆信息
    bool UpdateVehInfoInQueue(const CTransInfo &transInfo, bool bLast);
    //更新队列内最后一辆车
    bool ReplaceLastVehInfoInQueue(const CTransInfo &transInfo);

    //保存B7帧分省信息
    bool SetProvinceFeeGroupToLastTrans(int nIndex, quint32 OBUID,
                                        const QList<CProvinceFeeInfo> &FeeGroup);

    //交易完成************************

    bool CheckTheFirstVehInQueIsFrontRsu(QString &sPlate);
    void CompleteTrans(CTransResult bTransResult, int nIndex = 0, bool bAllowPass = true);

    void SetImageInfoByVcr(CTransInfo *pTransInfo);

    void EmitNotifyTransCompleteSign(CTransInfo *pTransInfo, bool bRedo);

    void ReCompleteTrans_HaveTrans(int nIndex, CTransInfo *pReserveTrans, bool bReserved = false);

    CTransInfo *GetCurTransInfo(int nIndex, quint32 nCheckVehQue = 1);

    CTransInfo *GetLastTransInfo(int nIndex);
    bool bHasWaitingTransVeh(int nDevIndex);

    bool HasTransOkVehInLane();

    SecureVerify *GetSecureVerify()
    {
        if (!m_pSecureVerify) m_pSecureVerify = new SecureVerify;
        return m_pSecureVerify;
    }

    quint32 GetLaneStatus() { return m_LaneStatus; }
    quint8 GetWasteSn(bool bPeek);

    void GetVehQueue(QList<CTransInfo> &TransList);
    void GetFronVehQueue(QList<CTransInfo> &TransList);
    //取上一个光栅状态
    QString GetLastLoopStatus();
    //取最新的光栅状态
    QString GetCurLoopStatus();
    bool bAllowllContinuePass(bool bDownBar);
    bool SaveAutoVLPResultToVehQue(int nDevIndex, const CAutoRegInfo &regInfo,
                                   bool bWhiteList = true);
    //判断是否为已经交易，发送完流水的车辆)
    bool IsTheLastVeh(const QString &sVehplate, int nColor);

public:
    bool saveTransWaste_EN(CTransInfo *pTransInfo);
    bool saveQuanFanChe_En(CTransInfo *pTransInfo);
    // 1-上班2-下班
    bool saveLoginWaste_En(const QDateTime &loginTime, quint8 bMsgCode);
    // 1-启动2-关闭
    bool SaveAppStartWaste_En(QDateTime &OccurTime, quint8 bMsgCode);
    // 20-下班 21 -上班
    bool InvoceWaste_En(quint8 bMsgCode, QDateTime &occurTime, CTransInfo *pTransInfo);

    bool MotorWaste_Entry(quint8 bMsgId, QDateTime occurTime, CTransInfo *pTransInfo);
    bool MotorWaste_Exit(quint8 bMsgId, QDateTime occurTime, CTransInfo *pTransInfo);

    //**************************

    bool saveHedgeWaste_Ex(CTransInfo *pTransInfo, const QString &sSourceId);
    bool saveTransWaste_EX(CTransInfo *pTransInfo);

    bool saveTmpTransWaste_Ex(CTransInfo *pTransInfo);

    //发送补费流水
    bool saveTransWaste_Repay(CTransInfo *pTransInfo);

    //保存tac 流水 bType =0 notac 1，fortac
    bool saveTransWaste_Tac(CTransInfo *pTransInfo, quint8 bType);

    // 1-上班2-下班
    bool saveLoginWaste_Ex(const QDateTime &loginTime, quint8 bMsgCode);

    // 1-启动2-关闭
    bool SaveAppStartWaste_Ex(QDateTime &OccurTime, quint8 bMsgCode);
    // 20-下班 21 -上班
    bool InvoceWaste_Ex(quint8 bMsgCode, QDateTime &occurTime, CTransInfo *pTransInfo);

    bool SendWorkSum_En(const CShiftSumInfo &ShiftSumInfo);
    bool SendWorkSum_Ex(const CShiftSumInfo &ShiftSumInfo);
    //车道是否空闲（不含未通过车辆）
    bool LaneIsIdle();
    //处理闯关
    int DoViolate(int nViolateId);

    //发送车道事件报文
    bool SaveLaneEventMsg(qint32 nMsgType);

    //门架流水
    bool SaveDoorFrameWaste_New(CTransInfo *pTransInfo);

    //发送门架合计数报文
    bool SendGantrySumInfo(const CGantrySumInfo_H &SumInfo, const QString &sLaneId);

    //日志上传
    bool UploadLog1();

    //换上发票
    void InstallInvoice(QString batchNum, quint64 startNum, quint32 count);
    //换下发票
    void UninstallInvoice();
    //是否有发票
    bool haveInvoice();
    //    //生成二维码
    //    void SaveQrCode(const CTransInfo &transInfo);
    bool FillLaneBasicInfo_remote(protocol::LaneInfo *pLaneInfo, const QDateTime &occurTime);

    void StopVehInputCardMgrTimer();
private:
signals:
    void NotifySetAllowPass(bool bForceUp, bool bOutTime);
    void NotifySetRefusePass();

private slots:
    void OnSetRefusePass();
    void OnSetAllowPass(bool bForceUp, bool bOutTime);

protected:  //填国标报文
    bool FillLaneBasicInfo(const QDateTime &OccurTime, CBasicInfo_Lane &basicInfo,
                           const QString &TransCode, CTransInfo *pTransInfo = NULL);
    bool FillLaneVehInfo(CTransInfo *pTransInfo, CVehInfo_Lane &vehInfo);
    bool FillIccInfoLane(CTransInfo *pTransInfo, CICCInfo_Lane &IccInfo);
    bool FillPayCardInfoLane(CTransInfo *pTransInfo, CPayCardInfo_Lane &payCardInfo, bool bEn);
    bool FillDealInfoLane(CTransInfo *pTransInfo, char Deal[256], CDealInfo_Lane &DealInfo);
    bool FillWeightInfoLane(CTransInfo *pTransInfo, CWeightInfo_Lane &WeightInfo);
    bool FillAuthInfoLane(CAuthInfo_Lane &AuthInfo, CTransInfo *pTransInfo = NULL);
    bool FillEnAddInfoLane(CTransInfo *pTransInfo, CEnAddInfo_Lane &addInfo);
    bool FillExAddInfoLane(CTransInfo *pTransInfo, CExAddInfo_Lane &addInfo);
    bool FillVehicleOutlineInfoLane(CTransInfo *pTransInfo, CEnAddInfo_Lane &outlineInfo);

    bool FillVehEnInfoLane(CTransInfo *pTransInfo, CVehEnInfo_Lane &VehEnInfo);
    bool FillTollBaseInfoLane(CTransInfo *pTransInfo, CTollBaseInfo_Lane &TollBaseInfo);
    bool FillKeyInfo(CKeyInfo_Lane &keyInfo);

    bool FillTollFeeInfoLane(CTransInfo *pTransInfo, CTollFeeInfo_Lane &TollFeeInfo);
    bool FillInvoiceInfoLane(CTransInfo *pTransInfo, CInvoiceInfo_Lane &invoiceInfo);
    bool FiillTollIntervalInfo(CTransInfo *pTransInfo, CTollIntervalInfo_Lane &TollInterval);
    bool FiillTollIntervalInfo_Repay(CTransInfo *pTransInfo, CTollIntervalInfo_Lane &TollInterval);

    bool FillIntervalInfo_Lane(const QString &sId, CTransInfo *pTransInfo,
                               QList<CSplitProvince_Lane> &splitInfo);

    bool FillIntervalInfo_Lane_Repay(const QString &sId, CTransInfo *pTransInfo,
                                     const CTollIntervalInfo_Lane &TollInterval,
                                     QList<CSplitProvince_Lane> &splitInfo);

    bool FillLaneHeart(CLaneHeart_LHBU_FD &msg);

    bool FillRealTimePass(CRealTimePass_RRU_FD &msg, CTransInfo &TransInfo);
    bool FillGantryBaseInfo(CDoorBaseInfo_BASEINFOUPLOAD &msg);

    bool FillGantryMsg_OBU(CTransInfo *pTransInfo, CDoorWaste_ETCTU_FD &doorMsg);
    bool FillGantryMsg_CPC(CTransInfo *pTransInfo, CDoorWaste_ETCTU_FD &doorMsg);
    // bool FillGantryMsg(CTransInfo *pTransInfo, CDoorWaste_ETCTU_FD &doorMsg);
    bool FillGantryMsg_Add_OBU(CTransInfo *pTransInfo, CDoorWaste_ETCTU_FD &doorMsg);
    bool FillGantryMsg_Add_CPC(CTransInfo *pTransInfo, CDoorWaste_ETCTU_FD &doorMsg);
    bool FillGantryMsg_Add(CTransInfo *pTransInfo, CDoorWaste_ETCTU_FD &doorMsg);

    bool FillGantryMsg_New(CTransInfo *pTransInfo, CDoorWaste_ETCTU_FD &doorMsg);

public:
    void StartVehInputCardMgrTimer();
signals:
    void NotifyLaneStatusChanged();
    //刷新上班时间
    void showOnDutyTime(const QDateTime &sOnDutyTime);
    //更新队列状态显示信号
    void NotifyVehQueChanged();
    //更新显示流水信号
    void NotifyTransCompleteETC(bool bRedo, int nViolateId, CTransInfo *pTranInfo);
    void NotifyFrontVehQueChanged();
    // bType 0-抬杆 1-超时 2-落杆
    void NotifyStayOutTime(QString sPlate, quint8 bType);
    void NotifyVehInvoice();
    void NotifySaveWaste();
    //通知停止卡机定时器(ETC交易成功时)
    void NotifyStopCardMgrTimer();



private:
    QString GetVehicleTypeDbInfo(CTransInfo *pTransInfo); // 获取车型库信息，构建vehicleTypeDbInfo字符串
    int CalculateVehicleType(CTransInfo *pTransInfo); // 根据车种、车型、轴数计算结果车型

    // 尝试获取最新的称重数据
    bool TryGetLatestWeightData(CTransInfo* pTransInfo, CWeightInfo_Lane& WeightInfo);
    quint32 GetWeightLimitByAxisNum(CVehClass vehClass, int nAxisNum, bool bZhuangXiang);

    // 根据轴数和车型确定轴型编码
    QString GetAxisTypeByAxisNum(int nAxisNum, int nVehClass);

    // 根据轴型编码和总重生成轴型信息字符串，并重新计算总重
    QString GenerateAxisInfoAndRecalcWeight(const QString& sAxisType, quint32 dwTotalWeight, quint32& dwNewTotalWeight);
    
    // 计算超重率
    void CalculateOverWeightRate(CWeightInfo_Lane& weightInfo, int nVehClass);
signals:

public slots:
    bool sendHeartbeat();
    void OnStayOutTimer();
    void OnDownBarTimer();
    void OnVehLeaveTimer();
    void OnVehDetectTimer();

protected:
    void SetLastLoopStatus(int ndevId, bool bStatus, QString sAllStatus);

    void StartDownBarTimer();

    void AddHaveTransVeh(const CTransInfo &transInfo);

    void StartVehLeaveTimer();
    void StopVehLeaveTimer();

    void StartVehDetectTimer();
    void StopVehDetectTimer();

private:
    //车辆过车队列，保存经过光栅的车辆
    QMutex m_transInfoMt;
    QList<CTransInfo *> m_VehQueue;

    //保存前天线交易完成，尚未经过光栅的车辆。
    QMutex m_WaitTransMt;
    QList<CTransInfo *> m_VehWaitQueue;

    CTransInfo m_curTransInfo;  //前天线正在交易车辆
    CTransInfo
        m_lastTransInfo;  //前天线上笔交易车辆,当B4帧处理完毕后，交易结果就从curTransInfo转到lastInfo.
                          //最后交易成功后，该交易信息保存到队列中。
    CTransInfo m_backTransInfo;      //后天线交易车辆
    CTransInfo m_backLastTransInfo;  //后天线、人工车道最终交易车辆

    CTransInfo m_manuTransInfo;  //人工处理车辆,交易完保存到m_backLastTransInfo中

    static CETCLaneCtrl *m_pETCLaneCtr;

    CAuthorizeInfo m_AuthorizeInfo;
    CShiftMgr m_ShiftMgr;
    quint32 m_nStationId;
    quint32 m_nLaneId;
    quint32 m_bLaneType;

    CLocalDataMgr *m_pDataMgr;

    QList<CAbstractState *> m_LaneStates;
    CLaneState_PSAMAuth *m_pPSAMAuthStaste;
    CLaneState_VehInput *m_pVehInputState;
    CLaneState_UnLogin *m_pUnLoginState;
    CLaneState_WaitVehPass *m_pWaitVehPassState;
    CLaneState_MotorCade *m_pMotorCadeState;
    CLaneState_VehMoney *m_pVehMoneyState;
    CLaneState_ETCState *m_pETCLaneState;
    SecureVerify *m_pSecureVerify;
    char m_DevInfo[64];
    quint8 m_TermCode[6];

    //分别对应前后车牌识别结果
    CAutoRegInfo m_ETCAutoRegInfos[2];
    QMutex m_ETCAutoRegMt;

    //最后一辆已经发送报文车辆信息（非抓拍识别信息)
    QMutex m_lastVehMt;
    CAutoRegInfo m_lastVehInfo;

    //登录操作员信息
    COperInfo m_OperInfo;

    //发票信息
    CInvoiceInfo m_InvoiceInfo;

    //车道状态
    qint32 m_LaneStatus;

    //光栅状态变化锁
    QMutex m_LoopStatusMt;
    //记录光栅变化状态，共四位，每两位代表一个光栅，1X光栅1 2x 光栅2
    QString m_sLastLoopStatus;
    QString m_sAllLoopStatus;  //"0|1|

    //车辆压上后线圈，防跳变定时器锁
    QMutex m_DownBarMt;
    QTimer *m_pDownBarTimer;

    //起落杆锁，锁起落杆和静态超时定时器
    QMutex m_lockBarMt;
    bool m_bBarState;  //栏杆状态 true 起杆 false-落杆
    //静态超时定时器
    QTimer *m_pStayOutTimer;

    //保存已经交易的车辆，包括成功和失败，该队列是从前头添加
    QMutex m_lockHaveTransVehMt;
    QList<CTransInfo> m_HaveTransVeh;

    QMutex m_ReserveMt;
    QList<CTransInfo> m_ReservedQue;  //倒车队列

    qint64 m_OnDetectTime;
    qint64 m_OnFrontTime;

    CUpdateServer m_UpdateServer;
    bool m_bVehLeadOut;
    bool m_bFrontRsuWork;

    bool m_bLongVeh;

    QTimer m_vehLeaveTimer;
    QMutex m_vehLeaveMx;

    QTimer m_vehDetectTimer;
    QMutex m_vehDetectMt;
};

#define Ptr_ETCCtrl CETCLaneCtrl::GetETCLaneCtrl()
#endif  // CETCLANECTRL_H
